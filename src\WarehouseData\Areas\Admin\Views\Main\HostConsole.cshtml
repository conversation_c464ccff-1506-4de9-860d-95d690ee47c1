﻿@{
	var dgPage = (Pek.Webs.HttpContext.Current.Request.RouteValues["controller"] + "_" + Pek.Webs.HttpContext.Current.Request.RouteValues["action"]).ToLower();

	// Css
	Html.AppendCssFileParts(ResourceLocation.Head, "/css/other/console1.css");

	// Script
	Html.AppendScriptParts(ResourceLocation.Footer, "~/js/Storage.js");
	Html.AppendScriptParts(ResourceLocation.Footer, "~/js/initSignalr.js");
}


<script asp-location="Footer">
    const storage = new Storage();  // new Storage(3)

    var currentDomain = window.location.protocol + "//" + window.location.hostname;

    if (window.location.port !== "") {
        currentDomain = currentDomain + ":" + window.location.port;
    }

    var notifyUrl = currentDomain;

    var token = storage.get("AccessToken");
    var dgpage = '@dgPage';



</script>