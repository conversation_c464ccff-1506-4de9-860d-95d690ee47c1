﻿using DG.QiNiu.Extensions;
using DG.Web.Framework;
using DH;
using DH.Entity;
using HlktechIoT.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;
using NewLife.Log;
using NPOI.HPSF;
using Pek;
using Pek.Configs;
using Pek.Helpers;
using Pek.Models;
using Pek.Timing;
using System.ComponentModel;

using XCode.Membership;

using YRY.Web.Controllers.Common;
using static iTextSharp.text.pdf.AcroFields;

namespace HlktechIoT.Areas.Production.Controllers
{
    /// <summary>产品型号</summary>
    [DisplayName("产品型号")]
    [Description("产品型号")]
    [ProductionArea]
    [DHMenu(100, ParentMenuName = "ProductionsManager", ParentMenuDisplayName = "生产管理", ParentMenuUrl = "", ParentMenuOrder = 30, ParentIcon = "layui-icon-component", CurrentMenuUrl = "~/{area}/ProductModel", CurrentMenuName = "ProductModelList", LastUpdate = "20250607")]
    public class ProductModelController : BaseAdminControllerX
    {

        /// <summary>
        /// 产品型号列表
        /// </summary>
        /// <returns></returns>
        [DisplayName("列表")]
        [EntityAuthorize(PermissionFlags.Detail)]
        public IActionResult Index()
        {
            return View();
        }

        /// <summary>
        /// 产品型号列表
        /// </summary>
        /// <param name="FirmwaresId">生产固件id</param>
        /// <param name="Name">名称</param>
        /// <param name="page">页码</param>
        /// <param name="limit">条数</param>
        /// <returns></returns>
        [DisplayName("列表")]
        [EntityAuthorize(PermissionFlags.Detail)]
        public IActionResult GetList(Int64 FirmwaresId, String Name, Int32 page, Int32 limit)
        {
            var pages = new PageParameter
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "Id",
                Desc = true,
            };
            var data = ProductType.Search(FirmwaresId, Name, null, DateTime.MinValue, DateTime.MinValue, "", pages);
            return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
        }

        /// <summary>
        /// 搜索用户
        /// </summary>
        /// <param name="keyword">关键字</param>
        /// <param name="page">页码</param>
        /// <param name="Id">用户id</param>
        /// <returns></returns>
        [DisplayName("搜索用户")]
        [EntityAuthorize(PermissionFlags.Detail)]
        public IActionResult SearchUser(String keyword, Int32 page, Int32 Id)
        {
            var res = new DResult();

            var pages = new PageParameter
            {
                PageIndex = page,
                PageSize = 10,
                RetrieveTotalCount = true,
                Sort = UserE._.ID,
                Desc = true,
            };

            res.data = UserE.Searchs(false, pages, 0, keyword).Select(e =>
            {
                return new Xmselect<Int32>
                {
                    name = e.DisplayName,
                    value = e.ID
                };
            });

            var model = UserE.FindByID(Id);
            if (model != null)
            {
                res.extdata = new { pages.PageCount, data = new List<NameValueL<Int32?>>() { new() { name = model.DisplayName, value = model.ID } } };
            }
            else
            {
                res.extdata = new { pages.PageCount };
            }

            res.success = true;

            return Json(res);
        }

        /// <summary>
        /// 添加产品型号
        /// </summary>
        /// <returns></returns>
        [DisplayName("添加")]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult Add()
        {
            return View();
        }

        /// <summary>
        /// 添加产品型号
        /// </summary>
        /// <param name="Name">名称</param>
        /// <param name="Status">启用状态</param>
        /// <param name="Remark">备注</param>
        /// <param name="NeedSn">是否生产sn</param>
        /// <param name="NeedMac">是否生产主mac</param>
        /// <param name="NeedMac1">是否生产副mac</param>
        /// <param name="PCBCycle">PCB周期</param>
        /// <param name="ShieldCycle">屏蔽罩周期</param>
        /// <param name="MainChipCycle">主芯片周期</param>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("添加")]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult Add(String Name, Boolean Status, String Remark, Boolean NeedSn, Boolean NeedMac, Boolean NeedMac1, Boolean PCBCycle, Boolean ShieldCycle, Boolean MainChipCycle)
        {
            DResult res = new();
            if (Name.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("产品型号名称不能为空");
                return Json(res);
            }
            if (NeedSn == false && NeedMac == false && NeedMac1 == false)
            {
                res.msg = GetResource("请至少启用一个SN或MAC或MAC1");
                return Json(res);
            }
            var model = ProductType.FindByName(Name);
            if (model != null)
            {
                res.msg = GetResource("产品型号名称已存在");
                return Json(res);
            }
            model = new ProductType
            {
                Name = Name,
                Status = Status,
                Remark = Remark,
                NeedSn = NeedSn,
                NeedMac = NeedMac,
                NeedMac1 = NeedMac1,
                PCBCycle = PCBCycle,
                ShieldCycle = ShieldCycle,
                MainChipCycle = MainChipCycle,
            };
            model.Insert();
            res.success = true;
            res.msg = GetResource("添加成功");
            return Json(res);
        }

        /// <summary>
        /// 编辑产品型号
        /// </summary>
        /// <param name="Id">型号id</param>
        /// <returns></returns>
        [DisplayName("编辑")]
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult Update(Int32 Id)
        {
            var model = ProductType.FindById(Id);
            if (model == null)
            {
                return Content(GetResource("产品型号不存在"));
            }
            return View(model);
        }

        /// <summary>
        /// 编辑产品型号
        /// </summary>
        /// <param name="Id">型号id</param>
        /// <param name="Status">启用状态</param>
        /// <param name="Name">名称</param>
        /// <param name="Remark">备注</param>
        /// <param name="NeedSn">是否生产sn</param>
        /// <param name="NeedMac">是否生产主mac</param>
        /// <param name="NeedMac1">是否生产副mac</param>
        /// <param name="PCBCycle">PCB周期</param>
        /// <param name="ShieldCycle">屏蔽罩周期</param>
        /// <param name="MainChipCycle">主芯片周期</param>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("编辑")]
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult Update(Int32 Id, Boolean Status, String Name, String Remark, Boolean NeedSn, Boolean NeedMac, Boolean NeedMac1, Boolean PCBCycle, Boolean ShieldCycle, Boolean MainChipCycle)
        {
            DResult res = new();
            if (Name.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("产品型号名称不能为空");
                return Json(res);
            }
            if (NeedSn == false && NeedMac == false && NeedMac1 == false)
            {
                res.msg = GetResource("请至少启用一个SN或MAC或MAC1");
                return Json(res);
            }
            var model = ProductType.FindById(Id);
            if (model == null)
            {
                res.msg = GetResource("产品型号不存在");
                return Json(res);
            }
            var modelProductType = ProductType.FindByName(Name);
            if (modelProductType != null && modelProductType.Id != model.Id)
            {
                res.msg = GetResource("产品型号名称已存在");
                return Json(res);
            }
            model.Name = Name;
            model.Remark = Remark;
            model.NeedSn = NeedSn;
            model.NeedMac = NeedMac;
            model.NeedMac1 = NeedMac1;
            model.Status = Status;
            model.PCBCycle = PCBCycle;
            model.ShieldCycle = ShieldCycle;
            model.MainChipCycle = MainChipCycle;
            model.Update();
            res.success = true;
            res.msg = GetResource("编辑成功");
            return Json(res);
        }

        /// <summary>
        /// 修改产品型号启用状态
        /// </summary>
        /// <param name="Id">型号id</param>
        /// <param name="Status">启用状态</param>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("修改状态")]
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult ModifyState(Int32 Id, Boolean Status)
        {
            var result = new DResult();

            var model = ProductType.FindById(Id);
            if (model == null)
            {
                result.msg = GetResource("状态调整出错");
                return Json(result);
            }

            model.Status = Status;
            model.Update();

            result.success = true;
            result.msg = GetResource("状态调整成功");

            return Json(result);
        }

        /// <summary>
        /// 删除产品型号
        /// </summary>
        /// <param name="Id">型号id</param>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("删除")]
        [EntityAuthorize(PermissionFlags.Delete)]
        public IActionResult Delete(Int32 Id)
        {
            var res = new DResult();
            ProductType.Delete(ProductType._.Id == Id);
            res.success = true;
            res.msg = GetResource("删除成功");
            return Json(res);

        }

        /// <summary>
        /// 问题反馈列表
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [DisplayName("反馈列表")]
        [EntityAuthorize((PermissionFlags)16)]
        public IActionResult FeedbackIndex(Int32 Id)
        {
            var model = ProductType.FindById(Id);
            if (model == null)
            {
                return Content(GetResource("产品型号不存在"));
            }
            return View(model);
        }

        /// <summary>
        /// 问题反馈列表
        /// </summary>
        /// <param name="Name">关键字</param>
        /// <param name="typeId">型号id</param>
        /// <param name="page">页码</param>
        /// <param name="limit">条数</param>
        /// <param name="Status">状态</param>
        /// <returns></returns>
        [DisplayName("反馈列表")]
        [EntityAuthorize((PermissionFlags)16)]
        public IActionResult FeedbackList(String Name, Int32 typeId, Int32 page, Int32 limit,Int32 Status = -1)
        {
            var pages = new PageParameter
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "Id",
                Desc = true,
            };
            var data = ProductionFeedback.Search(Status, Name, typeId, DateTime.MinValue, DateTime.MinValue, "", pages);
            return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
        }

        /// <summary>
        /// 上传反馈文件
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        [HttpPost]
        [EntityAuthorize((PermissionFlags)128)]
        public IActionResult UploadFiles(IFormFile file)
        {
            var res = new DResult();

            try
            {
                if (file == null)
                {
                    res.msg = GetResource("文件不能为空");
                    return Json(res);
                }
                
                var OrignfileName = file.FileName;

                var filename = (OrignfileName + UnixTime.ToTimestamp()).MD5(); ;

                using var ms = new MemoryStream();
                file.CopyTo(ms);
                byte[] fileBytes = ms.ToArray();

                var result = QiniuCloud.UploadData(filename, fileBytes);

                if(result.Code != 200)
                {
                    XTrace.WriteLine($"文件上传失败：{result.Message}");
                    res.msg = GetResource("文件上传失败");
                    return Json(res);
                }

                var filepath = Path.Combine(OssSetting.Current.QiNiu.BasePath ?? "", filename).Replace("\\", "/");

                res.msg = GetResource("文件上传成功");
                res.success = true;
                res.data = new { OriginFileName = OrignfileName, FilePath = filepath, FileUrl = Path.Combine(OssSetting.Current.QiNiu.Domain ?? "", filepath) };
                return Json(res);
            }
            catch (Exception ex)
            {
                XTrace.WriteLine($"文件上传异常：{ex.ToString()}");
                res.msg = GetResource("文件上传异常");
                return Json(res);
            }
        }

        /// <summary>
        /// 添加问题反馈
        /// </summary>
        /// <param name="ProductTypeId">产品型号id</param>
        /// <returns></returns>
        [DisplayName("添加反馈")]
        [EntityAuthorize((PermissionFlags)128)]
        public IActionResult FeedbackAdd(Int32 ProductTypeId)
        {
            var model = ProductType.FindById(ProductTypeId);
            if(model == null)
            {
                return Content(GetResource("产品型号不存在"));
            }
            return View(model);
        }

        /// <summary>
        /// 添加问题反馈
        /// </summary>
        /// <param name="ProductTypeId">产品型号id</param>
        /// <param name="Name">问题标题</param>
        /// <param name="Content">问题内容</param>
        /// <param name="PicPath">图片</param>
        /// <param name="VideoPath">视频</param>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("添加反馈")]
        [EntityAuthorize((PermissionFlags)128)]
        public IActionResult FeedbackAdd(Int32 ProductTypeId,String Name,String Content,String PicPath,String VideoPath)
        {
            DResult res = new();
            if(ProductTypeId <= 0)
            {
                res.msg = GetResource("产品型号id不能为空");
                return Json(res);
            }
            if (Name.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("问题标题不能为空");
                return Json(res);
            }
            if (Content.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("问题内容不能为空");
                return Json(res);
            }
            var modelProductType = ProductType.FindById(ProductTypeId);
            if (modelProductType == null)
            {
                res.msg = GetResource("产品型号不存在");
                return Json(res);
            }
            var model = new ProductionFeedback
            {
                ProductTypeId = ProductTypeId,
                Name = Name,
                Content = Content,
                PicPath = PicPath,
                VideoPath = VideoPath,
                Status = 0, // 默认状态为0
            };
            model.Insert();
            res.success = true;
            res.msg = GetResource("添加成功");
            return Json(res);
        }

        /// <summary>
        /// 编辑问题反馈
        /// </summary>
        /// <param name="Id">问题反馈id</param>
        /// <returns></returns>
        [DisplayName("编辑反馈")]
        [EntityAuthorize((PermissionFlags)32)]
        public IActionResult FeedbackUpdate(Int32 Id)
        {
            var model = ProductionFeedback.FindById(Id);
            if (model == null)
            {
                return Content(GetResource("问题反馈不存在"));
            }
            return View(model);
        }

        /// <summary>
        /// 编辑问题反馈
        /// </summary>
        /// <param name="Id">问题反馈id</param>
        /// <param name="Status">状态</param>
        /// <param name="Cause">处理原因</param>
        /// <param name="Name">问题标题</param>
        /// <param name="Content">问题内容</param>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("编辑反馈")]
        [EntityAuthorize((PermissionFlags)32)]
        public IActionResult FeedbackUpdate(Int32 Id,Int16 Status,String Cause, String Name, String Content)
        {
            DResult res = new();

            if (Name.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("问题标题不能为空");
                return Json(res);
            }
            if (Content.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("问题内容不能为空");
                return Json(res);
            }

            var model = ProductionFeedback.FindById(Id);

            if(model == null)
            {
                res.msg = GetResource("问题反馈不存在");
                return Json(res);
            }

            model.Name = Name;
            model.Content = Content;
            model.Status = Status;
            model.Cause = Cause;
            model.Update();

            res.success = true;
            res.msg = GetResource("编辑成功");
            return Json(res);
        }

        /// <summary>
        /// 删除问题反馈
        /// </summary>
        /// <returns></returns>
        [DisplayName("删除反馈")]
        [EntityAuthorize((PermissionFlags)64)]
        public IActionResult FeedbackDelete(Int32 Id)
        {
            var res = new DResult();
            var model = ProductionFeedback.FindById(Id);
            if (model == null)
            {
                res.msg = GetResource("问题反馈不存在");
                return Json(res);
            }
            if (!model.PicPath.IsNullOrWhiteSpace())
            {
                foreach (var item in model.PicPath.Split(","))
                {
                    QiniuCloud.Delete(OssSetting.Current.QiNiu.Domain + item);
                }
            }
            if (!model.VideoPath.IsNullOrWhiteSpace())
            {
                foreach (var item in model.VideoPath.Split(","))
                {
                    QiniuCloud.Delete(OssSetting.Current.QiNiu.Domain + item);
                }
            }
            model.Delete();
            res.success = true;
            res.msg = GetResource("删除成功");
            return Json(res);
        }
    }
}



