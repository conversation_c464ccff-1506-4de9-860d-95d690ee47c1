﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechIoT.Entity;

/// <summary>生产订单</summary>
public partial interface IProductOrders
{
    #region 属性
    /// <summary>编号</summary>
    Int64 Id { get; set; }

    /// <summary>订单号</summary>
    String OrderId { get; set; }

    /// <summary>产品型号编号</summary>
    Int32 ProductTypeId { get; set; }

    /// <summary>订单开始时间</summary>
    DateTime StartTime { get; set; }

    /// <summary>订单开始时间</summary>
    DateTime EndTime { get; set; }

    /// <summary>合作公司Id</summary>
    Int32 CompanyId { get; set; }

    /// <summary>数量</summary>
    Int32 Quantity { get; set; }

    /// <summary>补单数量</summary>
    Int32 ProductSupplementalQuantity { get; set; }

    /// <summary>审核状态 0待审核 1审核中 2已审核 3审核失败</summary>
    Int32 Status { get; set; }

    /// <summary>审核备注</summary>
    String? Remark { get; set; }

    /// <summary>Mac分配范围。使用Json存储多组start、end数据</summary>
    String? MacRange { get; set; }

    /// <summary>Mac分配范围中总的数量</summary>
    Int64 MacRangeCount { get; set; }

    /// <summary>审核时间</summary>
    DateTime AuditTime { get; set; }

    /// <summary>产品固件编号。必填</summary>
    Int64 FirmwaresId { get; set; }

    /// <summary>固件名称</summary>
    String? FirmwareName { get; set; }

    /// <summary>固件路径</summary>
    String? FirmwareFilePath { get; set; }

    /// <summary>订单状态数据 使用Json存储</summary>
    String? DataInfo { get; set; }

    /// <summary>创建者</summary>
    String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    String? UpdateIP { get; set; }
    #endregion
}
