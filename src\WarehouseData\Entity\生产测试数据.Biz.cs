﻿using NewLife;
using NewLife.Data;
using NewLife.Log;

using XCode;
using XCode.Shards;

namespace HlktechIoT.Entity;

public partial class DeviceTestLogs : CubeEntityBase<DeviceTestLogs>
{
    #region 对象操作
    static DeviceTestLogs()
    {
        // 按月分表
        Meta.ShardPolicy = new TimeShardPolicy(nameof(CreateTime), Meta.Factory)
        {
            TablePolicy = "{0}_{1:yyyy}",
            Step = TimeSpan.FromDays(365),
        };

        // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
        //var df = Meta.Factory.AdditionalFields;
        //df.Add(nameof(BatchNum));

        // 过滤器 UserModule、TimeModule、IPModule
        Meta.Modules.Add(new UserModule { AllowEmpty = false });
        Meta.Modules.Add<TimeModule>();
        Meta.Modules.Add(new IPModule { AllowEmpty = false });

        // 实体缓存
        // var ec = Meta.Cache;
        // ec.Expire = 60;
    }

    /// <summary>验证并修补数据，返回验证结果，或者通过抛出异常的方式提示验证失败。</summary>
    /// <param name="method">添删改方法</param>
    public override Boolean Valid(DataMethod method)
    {
        //if (method == DataMethod.Delete) return true;
        // 如果没有脏数据，则不需要进行任何处理
        if (!HasDirty) return true;

        // 建议先调用基类方法，基类方法会做一些统一处理
        if (!base.Valid(method)) return false;

        // 在新插入数据或者修改了指定字段时进行修正

        // 处理当前已登录用户信息，可以由UserModule过滤器代劳
        /*var user = ManageProvider.User;
        if (user != null)
        {
            if (method == DataMethod.Insert && !Dirtys[nameof(CreateUserID)]) CreateUserID = user.ID;
            if (!Dirtys[nameof(UpdateUserID)]) UpdateUserID = user.ID;
        }*/
        //if (method == DataMethod.Insert && !Dirtys[nameof(CreateTime)]) CreateTime = DateTime.Now;
        //if (!Dirtys[nameof(UpdateTime)]) UpdateTime = DateTime.Now;
        //if (method == DataMethod.Insert && !Dirtys[nameof(CreateIP)]) CreateIP = ManageProvider.UserHost;
        //if (!Dirtys[nameof(UpdateIP)]) UpdateIP = ManageProvider.UserHost;

        return true;
    }

    ///// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
    //[EditorBrowsable(EditorBrowsableState.Never)]
    //protected override void InitData()
    //{
    //    // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
    //    if (Meta.Session.Count > 0) return;

    //    if (XTrace.Debug) XTrace.WriteLine("开始初始化DeviceTestLogs[生产测试数据]数据……");

    //    var entity = new DeviceTestLogs();
    //    entity.BatchNum = 0;
    //    entity.ModuleType = "abc";
    //    entity.ModuleId = "abc";
    //    entity.SdkVersion = "abc";
    //    entity.DeviceMac = "abc";
    //    entity.TestTime = "abc";
    //    entity.TestFactory = "abc";
    //    entity.TestPosition = "abc";
    //    entity.TestData = "abc";
    //    entity.SaleInfo = "abc";
    //    entity.CustomerInfo = "abc";
    //    entity.Reserve1 = "abc";
    //    entity.Reserve2 = "abc";
    //    entity.Insert();

    //    if (XTrace.Debug) XTrace.WriteLine("完成初始化DeviceTestLogs[生产测试数据]数据！");
    //}

    ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
    ///// <returns></returns>
    //public override Int32 Insert()
    //{
    //    return base.Insert();
    //}

    ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
    ///// <returns></returns>
    //protected override Int32 OnDelete()
    //{
    //    return base.OnDelete();
    //}
    #endregion

    #region 扩展属性
    /// <summary>根据mac地址查找</summary>
    /// <param name="deviceMac">mac地址</param>
    /// <returns>实体对象</returns>
    public static DeviceTestLogs? FindByDeviceMacWithTime(String? deviceMac)
    {
        if (deviceMac == null) return null;

        XTrace.WriteLine($"获取mac：{deviceMac}");

        return Find(_.DeviceMac == deviceMac & _.CreateTime > "2022-01-01".ToDateTime());
    }    
    
    /// <summary>获取所有数据</summary>
    /// <returns>实体集合</returns>
    public static IList<DeviceTestLogs> GetAll(String? selects = null)
    {
        return FindAll(_.CreateTime > "2022-01-01".ToDateTime(), null, selects);
    }

    /// <summary>获取指定mac地址和模型类型来判断</summary>
    /// <param name="deviceMac">mac地址</param>
    /// <param name="moduleType">模块型号</param>
    /// <returns>实体集合</returns>
    public static DeviceTestLogs? FindByDeviceMacAndReturnFactory(String deviceMac, String moduleType)
    {
        return Find(_.CreateTime > "2025-06-10".ToDateTime() & _.DeviceMac == deviceMac & _.ModuleType == moduleType);
    }

    /// <summary>按年份分批获取数据</summary>
    /// <param name="selects">查询字段</param>
    /// <param name="processor">数据处理委托，用于处理每批数据</param>
    /// <param name="startYear">开始年份，默认为2022</param>
    /// <param name="endYear">结束年份，默认为当前年份</param>
    /// <returns>总处理记录数</returns>
    public static Int32 GetAllByYearBatch(String? selects, Action<IList<DeviceTestLogs>> processor, Int32 startYear = 2022, Int32? endYear = null)
    {
        if (processor == null) throw new ArgumentNullException(nameof(processor));
        
        endYear ??= DateTime.Now.Year;
        var totalCount = 0;

        for (var year = startYear; year <= endYear; year++)
        {
            var startDate = new DateTime(year, 1, 1);
            var endDate = new DateTime(year, 12, 31, 23, 59, 59);
            
            XTrace.WriteLine($"正在处理{year}年的数据，时间范围：{startDate} - {endDate}");
            
            var yearData = FindAll(_.CreateTime >= startDate & _.CreateTime <= endDate, null, selects);
            if (yearData?.Count > 0)
            {
                XTrace.WriteLine($"{year}年数据量：{yearData.Count}");
                processor(yearData);
                totalCount += yearData.Count;
            }
            else
            {
                XTrace.WriteLine($"{year}年无数据");
            }
        }

        return totalCount;
    }

    /// <summary>按年份分批获取数据（异步版本）</summary>
    /// <param name="selects">查询字段</param>
    /// <param name="processor">数据处理委托，用于处理每批数据</param>
    /// <param name="startYear">开始年份，默认为2022</param>
    /// <param name="endYear">结束年份，默认为当前年份</param>
    /// <returns>总处理记录数</returns>
    public static async Task<Int32> GetAllByYearBatchAsync(String? selects, Func<IList<DeviceTestLogs>, Task> processor, Int32 startYear = 2022, Int32? endYear = null)
    {
        if (processor == null) throw new ArgumentNullException(nameof(processor));
        
        endYear ??= DateTime.Now.Year;
        var totalCount = 0;

        for (var year = startYear; year <= endYear; year++)
        {
            var startDate = new DateTime(year, 1, 1);
            var endDate = new DateTime(year, 12, 31, 23, 59, 59);
            
            XTrace.WriteLine($"正在处理{year}年的数据，时间范围：{startDate} - {endDate}");
            
            var yearData = FindAll(_.CreateTime >= startDate & _.CreateTime <= endDate, null, selects);
            if (yearData?.Count > 0)
            {
                XTrace.WriteLine($"{year}年数据量：{yearData.Count}");
                await processor(yearData);
                totalCount += yearData.Count;
            }
            else
            {
                XTrace.WriteLine($"{year}年无数据");
            }
        }

        return totalCount;
    }
    #endregion

    #region 高级查询

    /// <summary>高级查询</summary>
    /// <param name="ids"></param>
    /// <param name="batchNum">批次号</param>
    /// <param name="start">时间开始</param>
    /// <param name="end">时间结束</param>
    /// <param name="moduleId">模块id</param>
    /// <param name="moduleType">模块型号</param>
    /// <param name="sdkVersion">固件版本</param>
    /// <param name="deviceMac">mac地址</param>
    /// <param name="testFactory">检测工厂</param>
    /// <param name="saleInfo">销售信息</param>
    /// <param name="customerInfo">客户信息</param>
    /// <param name="deduplicateInfo">去重信息</param>
    /// <param name="returnFactory">是否返厂</param>
    /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
    /// <returns>实体列表</returns>
    public static IList<DeviceTestLogs> Search(String ids, String batchNum, DateTime start, DateTime end, String moduleId, String moduleType, String sdkVersion, String deviceMac, String testFactory, String saleInfo, String customerInfo, String deduplicateInfo, Boolean? returnFactory, PageParameter page)
    {
        var exp = new WhereExpression();

        if (!ids.IsNullOrWhiteSpace())
        {
            exp &= _.DId.In(ids.Split(","));
        }

        if (!batchNum.IsNullOrWhiteSpace())
        {
            exp &= _.BatchNum == batchNum;
        }
        
        exp &= _.CreateTime >= start;

        exp &= _.CreateTime <= end;

        if (!moduleId.IsNullOrWhiteSpace())
        {
            exp &= _.ModuleId.Contains(moduleId);
        }

        if (!moduleType.IsNullOrWhiteSpace())
        {
            exp &= _.ModuleType.Contains(moduleType);
        }

        if (!sdkVersion.IsNullOrWhiteSpace())
        {
            exp &= _.SdkVersion.Contains(sdkVersion);
        }

        if (!deviceMac.IsNullOrWhiteSpace())
        {
            exp &= _.DeviceMac.Contains(deviceMac);
        }

        if (!testFactory.IsNullOrWhiteSpace())
        {
            exp &= _.TestFactory.Contains(testFactory);
        }

        if (!saleInfo.IsNullOrWhiteSpace())
        {
            exp &= _.SaleInfo.Contains(saleInfo);
        }

        if (!customerInfo.IsNullOrWhiteSpace())
        {
            exp &= _.CustomerInfo.Contains(customerInfo);
        }

        if (!deduplicateInfo.IsNullOrWhiteSpace())
        {
            exp &= _.DeduplicateInfo.Contains(deduplicateInfo);
        }

        if (returnFactory.HasValue)
        {
            exp &= _.ReturnFactory == returnFactory.Value;
        }

        return FindAll(exp, page);
    }

    /// <summary>高级查询(调优查询性能)</summary>
    /// <param name="ids"></param>
    /// <param name="batchNum">批次号</param>
    /// <param name="start">时间开始</param>
    /// <param name="end">时间结束</param>
    /// <param name="moduleId">模块id</param>
    /// <param name="moduleType">模块型号</param>
    /// <param name="sdkVersion">固件版本</param>
    /// <param name="deviceMac">mac地址</param>
    /// <param name="testFactory">检测工厂</param>
    /// <param name="saleInfo">销售信息</param>
    /// <param name="customerInfo">客户信息</param>
    /// <param name="deduplicateInfo">去重信息</param>
    /// <param name="returnFactory">是否返厂</param>
    /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
    /// <returns>实体列表</returns>
    public static IList<DeviceTestLogs> SearchWithOptimize(String ids, String batchNum, DateTime start, DateTime end, String moduleId, String moduleType, String sdkVersion, String deviceMac, String testFactory, String saleInfo, String customerInfo, String deduplicateInfo, Boolean? returnFactory, PageParameter page)
    {
        var exp = new WhereExpression();

        if (!ids.IsNullOrWhiteSpace())
        {
            exp &= _.DId.In(ids.Split(","));
        }

        if (!batchNum.IsNullOrWhiteSpace())
        {
            exp &= _.BatchNum == batchNum;
        }

        exp &= _.CreateTime >= start;

        exp &= _.CreateTime <= end;

        if (!moduleId.IsNullOrWhiteSpace())
        {
            exp &= _.ModuleId.Contains(moduleId);
        }

        if (!moduleType.IsNullOrWhiteSpace())
        {
            exp &= _.ModuleType.Contains(moduleType);
        }

        if (!sdkVersion.IsNullOrWhiteSpace())
        {
            exp &= _.SdkVersion.Contains(sdkVersion);
        }

        if (!deviceMac.IsNullOrWhiteSpace())
        {
            exp &= _.DeviceMac.Contains(deviceMac);
        }

        if (!testFactory.IsNullOrWhiteSpace())
        {
            exp &= _.TestFactory.Contains(testFactory);
        }

        if (!saleInfo.IsNullOrWhiteSpace())
        {
            exp &= _.SaleInfo.Contains(saleInfo);
        }

        if (!customerInfo.IsNullOrWhiteSpace())
        {
            exp &= _.CustomerInfo.Contains(customerInfo);
        }

        if (!deduplicateInfo.IsNullOrWhiteSpace())
        {
            exp &= _.DeduplicateInfo.Contains(deduplicateInfo);
        }

        if (returnFactory.HasValue)
        {
            exp &= _.ReturnFactory == returnFactory.Value;
        }

        var list = FindAll(exp, page, "Id");

        var exp1 = new WhereExpression();

        exp1 &= _.CreateTime >= start;
        exp1 &= _.CreateTime <= end;

        exp1 &= _.Id.In(list.Select(x => x.Id));

        return FindAll(exp1, new PageParameter { Sort = page.Sort, Desc = page.Desc, PageSize = 0 });
    }

    #endregion

    #region 业务操作
    public IDeviceTestLogs ToModel()
    {
        var model = new DeviceTestLogs();
        model.Copy(this);

        return model;
    }

    /// <summary>根据编号查找</summary>
    /// <param name="dId">编号</param>
    /// <param name="dateTime"></param>
    /// <returns>实体列表</returns>
    public static DeviceTestLogs? FindByDIdWithTime(Int64 dId, DateTime dateTime)
    {
        if (dId < 0 || dateTime <= DateTime.MinValue) return null;

        return Find(_.DId == dId & _.CreateTime >= dateTime);
    }

    #endregion
}
