﻿using DG.QiNiu.Extensions;
using DG.Web.Framework;
using Microsoft.AspNetCore.Mvc;
using NewLife;
using NewLife.Log;
using Pek.Configs;
using Pek.Models;
using Pek.Timing;
using System.ComponentModel;
using XCode.Membership;
using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Admin.Controllers
{
    [DisplayName("存储配置")]
    [Description("存储配置")]
    [AdminArea]
    [DHMenu(99, ParentMenuName = "BaseSystem", CurrentMenuUrl = "~/{area}/StorageConfig", CurrentMenuName = "StorageConfig", LastUpdate = "20250521")]
    public class StorageConfigController : BaseAdminControllerX
    {
        protected static Int32 MenuOrder { get; set; } = 100;

        [DisplayName("列表")]
        [EntityAuthorize((PermissionFlags)32)]
        public IActionResult Index()
        {
            return View();
        }

        /// <summary>
        /// 编辑存储配置
        /// </summary>
        /// <param name="AccessKey"></param>
        /// <param name="SecretKey"></param>
        /// <param name="Bucket"></param>
        /// <param name="BasePath"></param>
        /// <param name="Domain"></param>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("编辑")]
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult UpdateInfo(String AccessKey, String SecretKey, String Bucket, String BasePath, String Domain)
        {
            OssSetting.Current.QiNiu.AccessKey = AccessKey;
            OssSetting.Current.QiNiu.SecretKey = SecretKey;
            OssSetting.Current.QiNiu.Bucket = Bucket;
            OssSetting.Current.QiNiu.BasePath = BasePath;
            OssSetting.Current.QiNiu.Domain = Domain;
            OssSetting.Current.Save();
            return Json(new DResult { success = true, msg = GetResource("编辑成功") });
        }

        /// <summary>
        /// 文件上传
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        [HttpPost]
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult UploadFiles(IFormFile file)
        {
            var res = new DResult();

            try
            {
                if (file == null)
                {
                    res.msg = GetResource("文件不能为空");
                    return Json(res);
                }

                var OrignfileName = file.FileName;

                var filename = (OrignfileName + UnixTime.ToTimestamp()).MD5(); ;

                using var ms = new MemoryStream();
                file.CopyTo(ms);
                byte[] fileBytes = ms.ToArray();

                var result = QiniuCloud.UploadData(filename, fileBytes);

                if (result.Code != 200)
                {
                    res.msg = GetResource("文件上传失败");
                    return Json(res);
                }

                var filepath = Path.Combine(OssSetting.Current.QiNiu.BasePath ?? "", filename).Replace("\\", "/");

                res.msg = GetResource("文件上传成功");
                res.success = true;
                res.data = new { OriginFileName = OrignfileName, FilePath = filepath, FileUrl = Path.Combine(OssSetting.Current.QiNiu.Domain ?? "", filepath) };
                return Json(res);
            }
            catch (Exception ex)
            {
                XTrace.WriteLine($"文件上传异常：{ex.ToString()}");
                res.msg = GetResource("文件上传异常");
                return Json(res);
            }
        }
    }
}
