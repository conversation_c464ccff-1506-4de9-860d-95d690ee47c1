﻿using DG.Web.Framework;
using Microsoft.AspNetCore.Mvc;
using Pek.Configs;
using Pek.Models;
using System.ComponentModel;
using YRY.Web.Controllers.Areas.Admin;

namespace HlktechIoT.Areas.Admin.Controllers
{
    [DisplayName("存储配置")]
    [Description("存储配置")]
    [AdminArea]
    [DHMenu(99, ParentMenuName = "BaseSystem", CurrentMenuUrl = "~/{area}/StorageConfig", CurrentMenuName = "StorageConfig", LastUpdate = "20250521")]
    public class StorageConfigController : BaseAdminControllerX
    {
        protected static Int32 MenuOrder { get; set; } = 100;

        public IActionResult Index()
        {
            return View();
        }

        [HttpPost]
        public IActionResult UpdateInfo(String AccessKey, String SecretKey, String Bucket, String BasePath, String Domain)
        {
            OssSetting.Current.QiNiu.AccessKey = AccessKey;
            OssSetting.Current.QiNiu.SecretKey = SecretKey;
            OssSetting.Current.QiNiu.Bucket = Bucket;
            OssSetting.Current.QiNiu.BasePath = BasePath;
            OssSetting.Current.QiNiu.Domain = Domain;
            OssSetting.Current.Save();
            return Json(new DResult { success = true, msg = GetResource("编辑成功") });
        }
    }
}
