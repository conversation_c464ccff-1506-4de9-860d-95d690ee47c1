﻿using DG.Web.Framework;

using Microsoft.AspNetCore.Mvc;

using Pek.Models;

using System.ComponentModel;

using XCode.Membership;

using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Production.Controllers;

/// <summary>电源生产数据</summary>
[DisplayName("电源生产数据")]
[Description("电源生产数据")]
[ProductionArea]
[DHMenu(50, ParentMenuName = "ProductionsManager", CurrentMenuUrl = "~/{area}/Power", CurrentMenuName = "PowerList", LastUpdate = "20250708")]
public class PowerController : BaseAdminControllerX {

    /// <summary>
    /// 电源生产数据列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("电源生产数据列表")]
    public IActionResult Index()
    {
        return View();
    }
}
