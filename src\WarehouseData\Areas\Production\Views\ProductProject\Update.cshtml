﻿@{
    Html.AppendTitleParts(T("添加产品项目").Text);

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
}
<style asp-location="true">
    .layui-input-block {
    margin-right: 40px;
    }

    .containers {
    padding-top: 30px;
    padding-left: 30px;
    }

    .layui-form-item.btn {
    padding-top: 10px;
    padding-left: 115px;
    }

    .label-width span {
    color: #f00;
    }

    .layui-form-label {
    width: 94px;
    white-space:nowrap;
    min-width:110px;
    }

    .upload {
    cursor: pointer;
    max-width: 120px;
    max-height: 90px;
    }
    .select{
    max-width: 420px !important;
    border: 1px solid #e6e6e6;
    }
.layui-form-checked.layui-checkbox-disabled[lay-skin=primary]>i {
    background: red !important;
}
</style>
<script asp-location="Head">
    var closeThis = '@T("关 闭 当 前")';
    var closeOther = '@T("关 闭 其 他")';
    var closeAll = '@T("关 闭 全 部")';

    var jsMenuStyle = "@T("菜单风格")";
    var jsTopStyle = "@T("顶部风格")";
    var jsMenu = "@T("菜单")";
    var jsView = "@T("视图")";
    var jsBanner = "@T("通栏")";
    var jsThroughColor = "@T("通色")";
    var jsFooter = "@T("页脚")";
    var jsMoreSettings = "@T("更多设置")";
    var jsOpen = "@T("开")";
    var jsClose = "@T("关")";
    var jsThemeColor = "@T("主题配色")";
    var layuiNoData = '@T("无数据")';
    var layuiAsc = "@T("升序")";
    var layuiDesc = "@T("降序")";

    var layuiPrint = '@T("打印")';
    var layuiExport = '@T("导出")';
    var layuiFilterColumn = '@T("筛选列")';
    var layuiArticlePage = '@T("条/页")';
    var layuiTotal = '@T("总共")';
    var layuiBtn = '@T("确定")';
    var layuiGoPage = '@T("到第")';
    var layuiPrev = '@T("上一页")';
    var layuiNext = '@T("下一页")';
</script>
<div class="containers">
    <form class="layui-form">

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("名称")</label>
            <div class="layui-input-inline" style="min-width:320px">
                <input type="text" name="Name" placeholder="@T("请输入名称")" autocomplete="off" class="layui-input" lay-filter="Versions" value="@Model.Name">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("产品型号")</label>
            <div class="layui-input-inline" style="min-width:320px">
                <div id="demo1" style=" width: 100%;"></div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("物料")</label>
            <div class="layui-input-inline" style="min-width:320px">
                <input type="text" name="Material" placeholder="@T("请输入物料")" autocomplete="off" class="layui-input" lay-filter="Versions" value="@Model.Material">
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">@T("启用")</label>
                <div class="layui-input-inline">
                    <input type="checkbox" name="Status" lay-filter="Status" lay-skin="switch" @(Model.Status ? "checked":"")>
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("备注")</label>
            <div class="layui-input-inline" style="min-width:320px">
                <input type="text" name="Remark" placeholder="@T("请输入备注")" autocomplete="off" class="layui-input" lay-filter="Versions" value="@Model.Remark">
            </div>
        </div>

        @{
            var listProductDataCategory = ProductDataCategory.FindAllByParentId(0).Where(e=>e.Status);
        }
        @foreach (var item in listProductDataCategory)
        {
            var listProductData = ProductDataCategory.FindAllByParentId(item.Id).Where(e=>e.Status);

            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">@item.Name</label>
                    <div class="layui-input-inline">
                        @foreach(var data in listProductData)
                        {
                            @if (!string.IsNullOrEmpty(Model.DataCategoryIds) && Model.DataCategoryIds.Contains($",{data.Id},"))
                            {
                                <input type="checkbox" name="DataCategoryIds[]" value="@data.Id" title="@data.Name" checked @(data.Must ? "disabled":"")>
                            }
                            else
                            {
                                <input type="checkbox" name="DataCategoryIds[]" value="@data.Id" title="@data.Name" @(data.Must ? "disabled":"")>
                            }
                        }
                    </div>
                 </div>
            </div>
        }

        <div class="layui-form-item btn">
            <input name="Id" value="@Model.Id" hidden/>
            <input type="button" lay-submit lay-filter="Submit" id="Submit" class="pear-btn pear-btn-primary pear-btn-normal" style="margin-left:20px" value="@T("保存")" />
        </div>
    </form>
</div>

<script asp-location="Footer">
    layui.use(['abp', 'form', 'dgcommon', 'dg', 'upload'], function () {
        var $ = layui.$;
        var abp = layui.abp;
        var form = layui.form;
        var os = layui.dgcommon;
        var dg = layui.dg;
        var laydate = layui.laydate;

        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.iframeAuto(index);

         var demo1 = xmSelect.render({
            el: '#demo1',
            radio: false, //设置单选
            name: 'ProductTypeId',
            paging: true,  // 是否翻页
            pageSize: 10,  // 每页数量
            filterable: true,  // 搜索模式
            remoteSearch: true,  // 远程搜索
            clickClose: true,
            pageRemote: true,  // 分页
            disabled: false, // 设置禁用
            remoteMethod: function (val, cb, show, pageIndex) 
            {  // 远程方法
                var obj = [];
                // 接口数据
                $.post('@Url.Action("SearchProductType")', { keyword: val, page: pageIndex,Id:'@Model.ProductTypeId' }, function (res) 
                {
                    // console.log('搜索后的数据',res);
                    if (res.success) {
                        if (res.data != null) {
                            if (res.extdata.data != null) {
                                demo1.setValue(res.extdata.data)// 传入一个-默认值-数组
                            }
                            cb(res.data, res.extdata.PageCount);
                        }
                    }
                    else {
                        cb(obj, 0);
                        os.error(res.data.msg);
                    }
                });
            },
            on: function (data) 
            {  
                // 监听选择
            }
        });

        form.on('submit(Submit)', function (data) {

            // 收集所有 DataCategoryIds[xx] 字段
            var ids = [];
            for (var key in data.field) {
                if (key.startsWith('DataCategoryIds[')) {
                    ids.push(data.field[key]);
                }
            }
            if (ids.length > 0) {
                data.field.DataCategoryIds = ids.join(',');
                // 可选：删除原有的 DataCategoryIds[xx] 字段
                for (var key in data.field) {
                    if (key.startsWith('DataCategoryIds[')) {
                        delete data.field[key];
                    }
                }
            }

            //console.log(data.field);

            data.field.Status = data.field.Status == 'on';

            var waitIndex = parent.layer.load(2);

            abp.ajax({
                url: "@Url.Action("Update")",
                contentType : "application/x-www-form-urlencoded; charset=utf-8",
                data: data.field,
                abpHandleError: false
            }).done(function (data) {
                if (!data.success) {
                    abp.notify.error(data.msg);
                    return false;
                }
                parent.layer.closeAll();
                dg.reload('tables');
                parent.layui.abp.notify.success(data.msg);
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;
        });

    });
</Script>