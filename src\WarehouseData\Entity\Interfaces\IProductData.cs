﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechIoT.Entity;

/// <summary>产品资料</summary>
public partial interface IProductData
{
    #region 属性
    /// <summary>编号</summary>
    Int32 Id { get; set; }

    /// <summary>项目Id</summary>
    Int32 ProductProjectId { get; set; }

    /// <summary>产品型号Id</summary>
    Int32 ProductTypeId { get; set; }

    /// <summary>资料类别Id1</summary>
    Int32 ProductDataCategoryId1 { get; set; }

    /// <summary>资料类别Id2</summary>
    Int32 ProductDataCategoryId2 { get; set; }

    /// <summary>资料路径</summary>
    String? FilePath { get; set; }

    /// <summary>备注</summary>
    String? Remark { get; set; }

    /// <summary>创建者</summary>
    String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    String? CreateIP { get; set; }

    /// <summary>更新者</summary>
    String? UpdateUser { get; set; }

    /// <summary>更新者</summary>
    Int32 UpdateUserID { get; set; }

    /// <summary>更新时间</summary>
    DateTime UpdateTime { get; set; }

    /// <summary>更新地址</summary>
    String? UpdateIP { get; set; }
    #endregion
}
