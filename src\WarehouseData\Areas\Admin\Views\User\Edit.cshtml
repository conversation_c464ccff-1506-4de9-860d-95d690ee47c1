﻿@{
    var modelUser = Model!.UserModel as User;
    var modelDetail = UserDetail.FindById(modelUser!.ID);

    var Roles = Model.Roles as IEnumerable<Role>;

    Html.AppendTitleParts(T("编辑用户").Text);

    Html.AppendCssFileParts(ResourceLocation.Head, "~/libs/pear/css/module/dtree/dtree.css");
    Html.AppendCssFileParts(ResourceLocation.Head, "~/libs/pear/css/module/dtree/font/dtreefont.css");
}

<style asp-location="true">
    .sex {
        min-width: 30px;
        /* margin-top:10px; */
        margin: 10px 0px 0px -20px;
    }
</style>

<div class="layui-tab layui-tab-brief" lay-filter="user-tab">
    <ul class="layui-tab-title">
        <li class="layui-this">@T("用户信息")</li>
        <li>@T("角色")</li>
        <li>@T("部门")</li>
    </ul>
    <div class="layui-tab-content">
        <div class="layui-tab-item layui-show">
            <form class="layui-form" lay-filter="user-form" style="padding: 10px 0 0 0;">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">@T("用户名")</label>
                        <div class="layui-input-inline">
                            <input type="text" name="name" lay-verType="tips" lay-verify="required" placeholder="@T("请输入用户名")" autocomplete="off" class="layui-input" value="@modelUser?.Name" disabled>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">@T("密码")</label>
                        <div class="layui-input-inline">
                            <input type="password" name="password" lay-verify="pass" lay-verType="tips" placeholder="@T("请输入密码")" autocomplete="new-password" class="layui-input width">
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">@T("邮箱")</label>
                        <div class="layui-input-inline">
                            <input type="text" name="Email" lay-verType="tips" lay-verify="myemail" placeholder="@T("请输入邮箱")" autocomplete="off" class="layui-input" value="@modelUser?.Mail">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">@T("手机号")</label>
                        <div class="layui-input-inline">
                            <input type="text" name="Mobile" lay-verType="tips" lay-verify="myphone" placeholder="@T("请输入手机号")" autocomplete="off" class="layui-input" value="@modelUser?.Mobile">
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">@T("姓名")</label>
                        <div class="layui-input-inline">
                            <input type="text" name="RealName" lay-verType="tips" placeholder="@T("请输入姓名")" autocomplete="off" class="layui-input" value="@modelUser?.DisplayName">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">@T("用户类型")</label>
                        <div class="layui-input-inline" style="width: 190px;">
                            <select name="UType">
                                <option value="">@T("请选择")</option>
                                <!option value="1" @(modelDetail.UType == UserKinds.No ? Html.Raw("selected") : "")>@T("无")</!option>
                                <!option value="2" @(modelDetail.UType == UserKinds.Vendor ? Html.Raw("selected") : "")>@T("供应商/厂商")</!option>
                                <!option value="3" @(modelDetail.UType == UserKinds.Agent ? Html.Raw("selected") : "")>@T("代理商")</!option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">@T("启用")</label>
                        <div class="layui-input-inline">
                            <input type="checkbox" name="Enabled" lay-filter="Enabled" lay-skin="switch" @(modelUser?.Enable == true ? "checked" : "")>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">@T("性别")</label>
                        <div class="layui-input-inline" style="display:flex;">
                            @if (modelUser?.Sex == SexKinds.男)
                            {
                                <input type="radio" name="Sex" value="1" lay-verType="tips" autocomplete="off" class="layui-input" checked>

                                <div class="sex">@T("男")</div>
                                <input type="radio" name="Sex" value="2" lay-verType="tips" autocomplete="off" class="layui-input">

                                <div class="sex">@T("女")</div>
                            }
                            else if (modelUser?.Sex == SexKinds.女)
                            {
                                <input type="radio" name="Sex" value="1" lay-verType="tips" autocomplete="off" class="layui-input">

                                <div class="sex">@T("男")</div>
                                <input type="radio" name="Sex" value="2" lay-verType="tips" autocomplete="off" class="layui-input" checked>

                                <div class="sex">@T("女")</div>
                            }
                            else
                            {
                                <input type="radio" name="Sex" value="1" lay-verType="tips" autocomplete="off" class="layui-input">

                                <div class="sex">@T("男")</div>
                                <input type="radio" name="Sex" value="2" lay-verType="tips" autocomplete="off" class="layui-input">

                                <div class="sex">@T("女")</div>
                            }
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <input type="hidden" name="Id" value="@modelUser?.ID" />
                        <button class="layui-btn layui-hide" lay-submit lay-filter="user-submit" id="user-submit">@T("提交")</button>
                    </div>
                </div>
            </form>
        </div>
        <div class="layui-tab-item">
            <div class="layui-form" style="padding: 10px 0 0 0;">
                <div class="layui-form-item">
                    <div class="layui-input-block" style="margin-left:0px;text-align: center;">
                        @foreach (var row in Roles!)
                        {
                            <input type="radio" name="role" value="@row.ID" title="@T(row.Name)" @(modelUser?.RoleID == row.ID ? "checked" : "")>
                        }
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-tab-item">
            <div style="overflow: auto;">
                <ul id="organization-tree" class="dtree"></ul>
            </div>
        </div>
    </div>
</div>

<script asp-location="Footer">
    layui.use(['abp', 'dg', 'form', 'element', 'dtree'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var dg = layui.dg;
        var dtree = layui.dtree;
        var element = layui.element;

        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.iframeAuto(index);

        form.verify({
            pass: function (value, item) {
                var exp = /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{8,20}$/;
                if (value && !exp.test(value)) {
                    return '@T("密码必须8到32位，并且必须包含字母和数字")';
                }
            },
            myemail: function (value, item) { //value：表单的值、item：表单的DOM对象
                if (value != "") {
                    if (!/^[a-z0-9._%-]+@@([a-z0-9-]+\.)+[a-z]{2,4}$|^1[3|4|5|7|8]\d{9}$/.test(value)) {
                        return '@T("邮箱格式错误")';
                    }
                }
            },
            myphone: function (value, item) {
                if (value != "") {
                    if (!/^1[3|4|5|6|7|8|9]\d{9}$/.test(value)) {
                        return '@T("手机号格式错误")';
                    }
                }
            }
        })

        dtree.render({
            elem: "#organization-tree",
            initLevel: "10",
            line: true,
            width: "100%",
            height: "350",
            checkbar: true,
            ficon: ["1", "-1"],
            icon: ["0", "2"],
            method: 'get',
            url: "@Url.Action("GetOrganizationUnitList", new { Id = modelUser?.ID })",
            response: {
                message: "msg",
                statusCode: 200,
                title: "displayName"
            },
            dataStyle: "layuiStyle",
            dataFormat: "list"
        });

        form.on('submit(user-submit)', function (data) {
            data.field.Enabled = data.field.Enabled == "on";//switch默认值是on
            data.field.AssignedRoleNames = getRoleNames();
            data.field.AssignedOrganizationUnitIds = getOrganizationUnitIds();

            var waitIndex = parent.layer.load(2);

            var url = "@Url.Action("EditUser")";
            abp.ajax({
                url: url,
                contentType: "application/x-www-form-urlencoded; charset=utf-8",
                data: data.field,
                abpHandleError: false
            }).done(function (data) {
                if (!data.success) {
                    abp.notify.error(data.msg);
                    return false;
                }
                parent.layer.closeAll();
                parent.active.reload();
                parent.layui.abp.notify.success(data.msg);
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;
        });

        element.on('tab(user-tab)', function (data) {
            parent.layer.iframeAuto(index);
        });

        window.submitForm = function () {
            $("#user-submit").click();
        }

        function getOrganizationUnitIds() {
            var selectedNode = dtree.getCheckbarNodesParam("organization-tree");
            var ids = selectedNode.map(function (d) { return d.nodeId });
            return ids;
        }

        function getRoleNames() {
            var roleNames = [];
            var _$roleCheckboxes = $("input[name='role']:checked");
            if (_$roleCheckboxes) {
                for (var roleIndex = 0; roleIndex < _$roleCheckboxes.length; roleIndex++) {
                    var _$roleCheckbox = $(_$roleCheckboxes[roleIndex]);
                    roleNames.push(_$roleCheckbox.val());
                }
            }
            return roleNames;
        }

    });
</script>