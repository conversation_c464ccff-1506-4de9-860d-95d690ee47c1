﻿@{
    Html.AppendTitleParts(T("生产测试数据").Text);

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/components/dynamic-operation-column.js");

    // Css
    Html.AppendCssFileParts(ResourceLocation.Head, "~/css/components/dynamic-operation-column.css");
}

<style asp-location="true">
    .layui-table td, .layui-table th {
        border-right: none;
    }

    .layui-card-body {
        padding-bottom: 1px;
    }

    .layui-layer-title {
        background-color: #fff;
    }

    .dtree-nav-item {
        padding-left: 0;
    }

    .layui-table-tool {
        z-index: 1;
    }

    xm-select {
        margin-top: 10px;
        line-height: 30px;
        min-height: 30px !important;
    }

    .layui-form-item {
        display: flex;
        flex-wrap: wrap;
        justify-content: center:
    }

    label {
        white-space: nowrap;
    }
</style>

<script asp-location="Head">
    var pleaseSelect = '@T("请选择")';
    var layuiPrint = '@T("打印")';
    var layuiExport = '@T("导出")';
    var layuiFilterColumn = '@T("筛选列")';
    var layuiArticlePage = '@T("条/页")';
    var layuiTotal = '@T("总共")';
    var layuiBtn = '@T("确定")';
    var layuiGoPage = '@T("到第")';
    var layuiPrev = '@T("上一页")';
    var layuiNext = '@T("下一页")';
    var layuiNoData = '@T("无数据")';
    var layuiAsc = '@T("升序")';
    var layuiDesc = '@T("降序")';
</script>

<form class="layui-form dg-form">
    <div class="layui-form-item" style="margin-bottom: 3px;">
        <label class="layui-form-label" style="width: auto;margin:10px 5px 0 10px;"> @T("批次号")：</label>
        <div class="layui-input-inline" style="width: auto;margin-top:10px;">
            <input type="text" name="batchNum" id="batchNum" placeholder="@T("请输入")" autocomplete="off" class="layui-input">
        </div>

        <div class="layui-inline" style="padding-top: 10px;">

            <label class="layui-form-label" style="width: auto;margin:0px 0px 0 10px;">@T("创建时间")：</label>
            <div class="layui-inline" id="ID-laydate-range">
                <div class="layui-input-inline">
                    <input type="text" name="start" id="start" readonly placeholder="@T("开始时间")" autocomplete="off" class="layui-input">
                </div>

                <div class="layui-form-mid">-</div>
                <div class="layui-input-inline">
                    <input type="text" name="end" id="end" readonly placeholder="@T("结束时间")" autocomplete="off" class="layui-input">
                </div>
            </div>
        </div>

        <label class="layui-form-label" style="width: auto;margin:10px 5px 0 10px;"> @T("模块id")：</label>
        <div class="layui-input-inline" style="width: auto;margin-top:10px;">
            <input type="text" name="moduleId" id="moduleId" placeholder="@T("请输入")" autocomplete="off" class="layui-input">
        </div>

        <label class="layui-form-label" style="width: auto;margin:10px 5px 0 10px;"> @T("模块型号")：</label>
        <div class="layui-input-inline" style="width: auto;margin-top:10px;">
            <input type="text" name="moduleType" id="moduleType" placeholder="@T("请输入")" autocomplete="off" class="layui-input">
        </div>

        <label class="layui-form-label" style="width: auto;margin:10px 5px 0 10px;"> @T("固件版本")：</label>
        <div class="layui-input-inline" style="width: auto;margin-top:10px;">
            <input type="text" name="sdkVersion" id="sdkVersion" placeholder="@T("请输入")" autocomplete="off" class="layui-input">
        </div>

        <label class="layui-form-label" style="width: auto;margin:10px 5px 0 10px;"> @T("mac地址")：</label>
        <div class="layui-input-inline" style="width: auto;margin-top:10px;">
            <input type="text" name="deviceMac" id="deviceMac" placeholder="@T("请输入")" autocomplete="off" class="layui-input">
        </div>

        <label class="layui-form-label" style="width: auto;margin:10px 5px 0 10px;"> @T("检测工厂")：</label>
        <div class="layui-input-inline" style="width: auto;margin-top:10px;">
            <input type="text" name="testFactory" id="testFactory" placeholder="@T("请输入")" autocomplete="off" class="layui-input">
        </div>

        <label class="layui-form-label" style="width: auto;margin:10px 5px 0 10px;"> @T("销售信息")：</label>
        <div class="layui-input-inline" style="width: auto;margin-top:10px;">
            <input type="text" name="saleInfo" id="saleInfo" placeholder="@T("请输入")" autocomplete="off" class="layui-input">
        </div>

        <label class="layui-form-label" style="width: auto;margin:10px 5px 0 10px;"> @T("客户信息")：</label>
        <div class="layui-input-inline" style="width: auto;margin-top:10px;">
            <input type="text" name="customerInfo" id="customerInfo" placeholder="@T("请输入")" autocomplete="off" class="layui-input">
        </div>

        <label class="layui-form-label" style="width: auto;margin:10px 5px 0 10px;"> @T("去重信息")：</label>
        <div class="layui-input-inline" style="width: auto;margin-top:10px;">
            <input type="text" name="deduplicateInfo" id="deduplicateInfo" placeholder="@T("请输入")" autocomplete="off" class="layui-input">
        </div>

        <label class="layui-form-label" style="width: auto;margin:10px 5px 0 10px;"> @T("是否返厂")：</label>
        <div class="layui-input-inline" style="width: auto;margin-top:10px;">
            <select name="returnFactory" id="returnFactory" lay-filter="returnFactory">
                <option value="">@T("请选择")</option>
                <option value="true">@T("是")</option>
                <option value="false">@T("否")</option>
            </select>
        </div>
    </div>
</form>

<div class="layui-card">
    <div class="layui-card-body">
        <table class="layui-hide" id="tablist" lay-filter="tool"></table>
    </div>
</div>

<script asp-location="Footer">
    // 设置变量保存选中行信息
    let ids = new Array();
    // 保存当前页全部数据id，点击全选时使用
    let tableIds = new Array();

    $("body").on("click", function (event) {
        $("div[dtree-id][dtree-select]").removeClass("layui-form-selected");
        $("div[dtree-id][dtree-card]").removeClass("dtree-select-show layui-anim layui-anim-upbit");
    });

    layui.use(['table', 'abp', 'dg', 'form', 'dgcommon', 'dtree'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;
        var os = layui.dgcommon;
        var dtree = layui.dtree;
        var laydate = layui.laydate;

        // 使用通用动态操作列组件
        var operationButtons = [
            { 
                text: '@T("查看")', 
                event: 'details', 
                class: 'pear-btn pear-btn-warming', 
                condition: function(d) { return true; }, 
                alwaysShow: true
            },
            @if (this.Has((PermissionFlags)8))
            {
                @:{ 
                @:    text: '@T("删除")', 
                @:    event: 'del', 
                @:    class: 'pear-btn pear-btn-danger', 
                @:    condition: function(d) { return true; },
                @:    alwaysShow: true
                @:}
            }
        ];

        // 初始化动态操作列组件
        var operationColumnWidth = window.dynamicOperationColumn.init({
            buttons: operationButtons,
            tableId: 'tablist',
            debug: true  // 开启调试模式
        });

        // 日期范围 - 左右面板独立选择模式
        laydate.render({
            elem: '#ID-laydate-range',
            range: ['#start', '#end'],
            type: 'datetime',       // 设置日期选择类型为年月
            format: 'yyyy-MM-dd HH:mm:ss',   // 设置日期的格式，这里是年-月
            done: function (value, date) {
                $("#start").val(value.split(" - ")[0]);
                $("#end").val(value.split(" - ")[1]);
                checkDateValidity();
            },
            choose: function (date) {
                console.log(date);
                // 用户选择日期的回调函数
                // 在这里可以处理用户选择日期后的逻辑
                laydate.close(); // 关闭日期选择器弹窗
            }
        });
        // 获取当前年份和日期
        var currentYear = new Date().getFullYear();
        var currentMonth = new Date().getMonth();
        var currentDate = new Date().toISOString().split('T')[0];

        table.render({
            elem: '#tablist'
            , url: '@Url.Action("GetList")'
            , page: true //开启分页
            , toolbar: '#user-toolbar'
            , defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print']
            , cellMinWidth: 60
            , cols: [[
                { type: 'checkbox', width: 60 }
                , { field: 'BatchNum', title: '@T("批次号")', width: 110 }
                , { field: 'ModuleId', title: '@T("模块id")', minWidth: 160 }
                , { field: 'ModuleType', title: '@T("模块型号")', width: 120 }
                , { field: 'SdkVersion', title: '@T("固件版本")', width: 240 }
                , { field: 'DeviceMac', title: '@T("mac地址")', minWidth: 130 }
                , { field: 'TestFactory', title: '@T("检测工厂")', minWidth: 160 }
                , { field: 'SaleInfo', title: '@T("销售信息")', width: 150 }
                , { field: 'CustomerInfo', title: '@T("客户信息")', width: 160 }
                , { field: 'DeduplicateInfo', title: '@T("去重信息")', width: 130 }
                , {
                    title: '@T("创建时间")', minWidth: 140, templet: (d) => {
                        if (d.CreateTime != undefined && d.CreateTime[0] != 0) {
                            return `<div>${d.CreateTime}</div>`
                        }
                        return `<div></div>`
                    }
                }
                , { fixed: 'right', title: ' @T("操作")', toolbar: '#tool', width: operationColumnWidth }
            ]]
            , limit: 13
            , limits: [10, 13, 20, 30, 50, 100]
            , height: 'full-100'
            , id: 'tables'
            , done: function (res) {
                // 设置当前页全部数据Id到全局变量
                tableIds = res.data.map(function (value) {
                    return value.Id;
                });

                // 设置当前页选中项
                $.each(res.data, function (idx, val) {
                    if (ids.indexOf(val.Id) > -1) {
                        val["LAY_CHECKED"] = 'true';
                        //找到对应数据改变勾选样式，呈现出选中效果
                        let index = val['LAY_INDEX'];
                        $('tr[data-index=' + index + '] input[type="checkbox"]').click();
                        form.render('checkbox'); //刷新checkbox选择框渲染
                    }
                });
                // 获取表格勾选状态，全选中时设置全选框选中
                let checkStatus = table.checkStatus('tables');
                if (checkStatus.isAll) {
                    $('.layui-table-header th[data-field="0"] input[type="checkbox"]').prop('checked', true);
                    form.render('checkbox'); //刷新checkbox选择框渲染
                }

                // 使用通用组件应用操作列宽度
                window.dynamicOperationColumn.delayApplyWidth(300, true);
            },
            where: {
                start: currentYear + '-' + (currentMonth + 1) + '-01 00:00:00'
            }
        });

        // 监听勾选事件
        table.on('checkbox(tool)', function (obj) {
            if (obj.checked == true) {
                if (obj.type == 'one') {
                    ids.push(obj.data.Id);
                } else {
                    for (let i = 0; i < tableIds.length; i++) {
                        //当全选之前选中了部分行进行判断，避免重复
                        if (ids.indexOf(tableIds[i]) == -1) {
                            ids.push(tableIds[i]);
                        }
                    }
                }
            } else {
                if (obj.type == 'one') {
                    let i = ids.length;
                    while (i--) {
                        if (ids[i] == obj.data.Id) {
                            ids.splice(i, 1);
                        }
                    }
                } else {
                    let i = ids.length;
                    while (i--) {
                        if (tableIds.indexOf(ids[i]) != -1) {
                            ids.splice(i, 1);
                        }
                    }
                }
            }
        });

        window.active = {
            reload: function () {
                table.reload('tables', {
                    page: {
                        curr: 1
                    },
                    where: {
                        batchNum: $("#batchNum").val(),
                        start: $("#start").val(),
                        end: $("#end").val(),
                        moduleId: $("#moduleId").val(),
                        moduleType: $("#moduleType").val(),
                        sdkVersion: $("#sdkVersion").val(),
                        deviceMac: $("#deviceMac").val(),
                        testFactory: $("#testFactory").val(),
                        saleInfo: $("#saleInfo").val(),
                        customerInfo: $("#customerInfo").val(),
                        deduplicateInfo: $("#deduplicateInfo").val(),
                        returnFactory: $("#returnFactory").val()
                    }
                })
            }
        }

        // 监听输入批次号
        $("#batchNum").on("input", function (e) {
            ids = [];
            active.reload('tables', {
                where: { "batchNum": $("#batchNum").val() },
            })
        });

        // 监听输入模块id
        $("#moduleId").on("input", function (e) {
            ids = [];
            active.reload('tables', {
                where: { "moduleId": $("#moduleId").val() },
            })
        });

        // 监听输入模块型号
        $("#moduleType").on("input", function (e) {
            ids = [];
            active.reload('tables', {
                where: { "moduleType": $("#moduleType").val() },
            })
        });

        // 监听输入固件版本
        $("#sdkVersion").on("input", function (e) {
            ids = [];
            active.reload('tables', {
                where: { "sdkVersion": $("#sdkVersion").val() },
            })
        });

        // 监听输入mac地址
        $("#deviceMac").on("input", function (e) {
            ids = [];
            active.reload('tables', {
                where: { "deviceMac": $("#deviceMac").val() },
            })
        });

        // 监听输入检测工厂
        $("#testFactory").on("input", function (e) {
            ids = [];
            active.reload('tables', {
                where: { "testFactory": $("#testFactory").val() },
            })
        });

        // 监听输入销售信息
        $("#saleInfo").on("input", function (e) {
            ids = [];
            active.reload('tables', {
                where: { "saleInfo": $("#saleInfo").val() },
            })
        });

        // 监听输入客户信息
        $("#customerInfo").on("input", function (e) {
            ids = [];
            active.reload('tables', {
                where: { "customerInfo": $("#customerInfo").val() },
            })
        });

        // 监听输入去重信息
        $("#deduplicateInfo").on("input", function (e) {
            ids = [];
            active.reload('tables', {
                where: { "deduplicateInfo": $("#deduplicateInfo").val() },
            })
        });

        // 监听是否返厂下拉选择
        form.on('select(returnFactory)', function (data) {
            ids = [];
            active.reload();
        });

        table.on('toolbar(tool)', function (obj) {
            let data = obj.config
            var that = this
            if (obj.event === 'add') {
                window.add(data);
            } else if (obj.event === 'refresh') {
                active.reload();
            } else if (obj.event === 'exportall') {
                // 检查start和end是否为空
                if ($("#start").val() === "" || $("#end").val() === "") {
                    layer.alert('开始时间和结束时间不能为空', { icon: 0 });
                    return;
                }

                // 检查其他查询条件至少有一个不为空
                if ($("#batchNum").val() === "" &&
                    $("#moduleId").val() === "" &&
                    $("#moduleType").val() === "" &&
                    $("#sdkVersion").val() === "" &&
                    $("#deviceMac").val() === "" &&
                    $("#testFactory").val() === "" &&
                    $("#saleInfo").val() === "" &&
                    $("#customerInfo").val() === "" &&
                    $("#deduplicateInfo").val() === "") {
                    layer.alert('请至少填写一个查询条件', { icon: 0 });
                    return;
                }

                // 创建表单
                var form = $('<form></form>').attr('action', '@Url.Action("ExportAll")').attr('method', 'POST');
                form.append($('<input>').attr('type', 'hidden').attr('name', 'ids').attr('value', ids.length <= 0 ? "" : ids.join(',')));
                form.append($('<input>').attr('type', 'hidden').attr('name', 'batchNum').attr('value', $("#batchNum").val()));
                form.append($('<input>').attr('type', 'hidden').attr('name', 'start').attr('value', $("#start").val()));
                form.append($('<input>').attr('type', 'hidden').attr('name', 'end').attr('value', $("#end").val()));
                form.append($('<input>').attr('type', 'hidden').attr('name', 'moduleId').attr('value', $("#moduleId").val()));
                form.append($('<input>').attr('type', 'hidden').attr('name', 'moduleType').attr('value', $("#moduleType").val()));
                form.append($('<input>').attr('type', 'hidden').attr('name', 'sdkVersion').attr('value', $("#sdkVersion").val()));
                form.append($('<input>').attr('type', 'hidden').attr('name', 'deviceMac').attr('value', $("#deviceMac").val()));
                form.append($('<input>').attr('type', 'hidden').attr('name', 'testFactory').attr('value', $("#testFactory").val()));
                form.append($('<input>').attr('type', 'hidden').attr('name', 'saleInfo').attr('value', $("#saleInfo").val()));
                form.append($('<input>').attr('type', 'hidden').attr('name', 'customerInfo').attr('value', $("#customerInfo").val()));
                form.append($('<input>').attr('type', 'hidden').attr('name', 'deduplicateInfo').attr('value', $("#deduplicateInfo").val()));
                form.append($('<input>').attr('type', 'hidden').attr('name', 'returnFactory').attr('value', $("#returnFactory").val()));
                // 将表单添加到body并提交
                form.appendTo('body').submit().remove();
            }
        });
        table.on('tool(tool)', function (obj) {
            var data = obj.data;
            if (obj.event === 'del') {
                parent.layer.confirm('@T("确认删除吗")?', { btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function (index) {
                    $.post('@Url.Action("Delete")', { Id: data.Id, Time: data.CreateTime }, function (data) {
                        if (data.success) {
                            abp.notify.success(data.msg);
                            active.reload();
                        } else {
                            abp.notify.warn(data.msg);
                        }
                    });
                    parent.layer.close(index);
                });
            } else if (obj.event === 'details') {
                window.details(data);
            }
        });

        window.saveCallback = function (data) {
            parent.layer.close(data.index);
            abp.notify.success(data.msg);
            table.reload("tables");
        }

        window.details = function (data) {
            layer.open({
                type: 1,
                title: ' @T("信息")',
                area: ['700px', '500px'],
                content: `
                        <form class="layui-form layui-layer-wrap" action="" id="detailDiv" style="margin-top: 20px;">
                        <div class="layui-form-item">
                            <label class="layui-form-label">@T("检测工位")：</label>
                            <div class="layui-input-inline">
                                <input class="layui-input" disabled="" id="test_position" value="${data.TestPosition}">
                            </div>
                        </div>
                            <div class="layui-form-item">
                            <label class="layui-form-label">@T("预留字段")1：</label>
                            <div class="layui-input-inline" style="width: 300px;">
                            <input class="layui-input" disabled="" id="reserve1" value="${data.Reserve1}">
                            </div>
                            </div>
                            <div class="layui-form-item">
                            <label class="layui-form-label">@T("预留字段")2：</label>
                            <div class="layui-input-inline" style="width: 300px;">
                            <input class="layui-input" disabled="" id="reserve2" value="${data.Reserve2}">
                            </div>
                            </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">@T("检测数据")：</label>
                            <div class="layui-input-inline">
                                <textarea id="test_data" rows="10" cols="65" disabled>${data.TestData}</textarea>
                            </div>
                        </div>
                    </form>
                `,
                btn: ['关闭'],
                yes: function (index, layero) {
                    layer.close(index);
                }
            });
        }

        //时间插件
        var startDate = laydate.render({
            elem: '#start',
            btns: ['clear', "confirm"],//只显示清空和确定按钮
            type: 'datetime',       // 设置日期选择类型为年月
            format: 'yyyy-MM-dd HH:mm:ss',   // 设置日期的格式，这里是年-月
            value: currentYear + '-' + (currentMonth + 1) + '-01 00:00:00', // 设置默认值为当年1月1日
            choose: function (date) {
                // 用户选择日期的回调函数
                // 在这里可以处理用户选择日期后的逻辑
                laydate.close(); // 关闭日期选择器弹窗
            }
        });

        var endDate = laydate.render({
            elem: '#end',
            btns: ["clear", "confirm"],
            type: 'datetime',       // 设置日期选择类型为年月
            format: 'yyyy-MM-dd HH:mm:ss',   // 设置日期的格式，这里是年-月
            value: currentDate + ' 23:59:59', // 设置默认值为当年1月1日
            choose: function (date) {
                console.log(date);
                // 用户选择日期的回调函数
                // 在这里可以处理用户选择日期后的逻辑
                laydate.close(); // 关闭日期选择器弹窗
            }
        });

        function checkDateValidity() {
            var startValue = $("#start").val();
            var endValue = $("#end").val();

            if (startValue && endValue) {

                // if (startValue.substr(0, 4) != endValue.substr(0, 4)) {
                //     os.warning('开始时间和结束时间必须在同一年，请重新选择。');
                //     $("#start").val(""); // 清空开始时间输入框
                //     $("#end").val("");   // 清空结束时间输入框
                //     return;
                // }

                //     console.log(   $("#start").val(),
                //         $("#end").val());
                // }
                ids = [];
                active.reload("tables")

            }
        }

        window.warning = function (msg) {
            os.warning(msg);
        }
    });
</script>

<script type="text/html" id="tool">
    <div class="operation-column">
        {{#  layui.each(window.operationButtons, function(index, button){ }}
            {{#  var isEnabled = button.condition(d); }}
            {{#  var buttonClass = button.class + ' pear-btn-xs'; }}
            {{#  if(!isEnabled){ }}
                {{#  buttonClass += ' disabled-button'; }}
            {{#  } }}
            <a class="{{buttonClass}}" lay-event="{{isEnabled ? button.event : 'disabled'}}" 
               title="{{!isEnabled ? '当前状态下不可操作' : ''}}"
               data-enabled="{{isEnabled}}">{{button.text}}</a>
        {{#  }); }}
    </div>
</script>

<script type="text/html" id="user-toolbar">
    @* <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
        <i class="layui-icon layui-icon-add-1"></i>
        @T("新增")
    </button> *@
    @if (this.Has((PermissionFlags)32))
    {
        <a class="pear-btn pear-btn-primary pear-btn-md" lay-event="exportall">
            <i class="layui-icon layui-icon-export"></i>
            @T("导出")
        </a>
    }
</script>