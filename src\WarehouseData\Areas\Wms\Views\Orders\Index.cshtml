﻿@{
    Html.AppendTitleParts(T("订单列表").Text);

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
}
<style asp-location="true">
    .layui-table td, .layui-table th {
        border-right: none;
    }

    .layui-card-body {
        padding-bottom: 1px;
    }

    .layui-layer-title {
        background-color: #fff;
    }

    .dtree-nav-item {
        padding-left: 0;
    }

    .layui-table-tool {
        z-index: 1;
    }

    xm-select {
        margin-top: 10px;
        line-height: 30px;
        min-height: 30px !important;
    }

    .layui-form-item {
        display: flex;
        flex-wrap: wrap;
        justify-content: center:
    }

    label {
        white-space: nowrap;
    }
</style>
<script asp-location="Head">
    var pleaseSelect = '@T("请选择")';
    var layuiPrint = '@T("打印")';
    var layuiExport = '@T("导出")';
    var layuiFilterColumn = '@T("筛选列")';
    var layuiArticlePage = '@T("条/页")';
    var layuiTotal = '@T("总共")';
    var layuiBtn = '@T("确定")';
    var layuiGoPage = '@T("到第")';
    var layuiPrev = '@T("上一页")';
    var layuiNext = '@T("下一页")';
    var layuiNoData = '@T("无数据")';
    var layuiAsc = '@T("升序")';
    var layuiDesc = '@T("降序")';
</script>

<form class="layui-form dg-form">
    <div class="layui-form-item" style="margin-bottom: 3px;">
        <label class="layui-form-label" style="width: auto;margin:10px 5px 0 10px;"> @T("订单号")：</label>
        <div class="layui-input-inline" style="width: auto;margin-top:10px;">
            <input type="text" name="key" id="key" placeholder="@T("请输入")" autocomplete="off" class="layui-input">
        </div>

        <div class="layui-inline" style="padding-top: 10px;">

            <label class="layui-form-label" style="width: auto;margin:0px 0px 0 10px;">@T("筛选时间")：</label>
            <div class="layui-inline" id="ID-laydate-range">
                <div class="layui-input-inline">
                    <input type="text" name="start" id="start" readonly placeholder="@T("开始时间")" autocomplete="off" class="layui-input">
                </div>

                <div class="layui-form-mid">-</div>
                <div class="layui-input-inline">
                    <input type="text" name="end" id="end" readonly placeholder="@T("结束时间")" autocomplete="off" class="layui-input">
                </div>
            </div>
        </div>

        <div>
            <label class="layui-form-label" style="width:30px;margin:10px 5px 0 10px;">@T("用户")：</label>
            <div class="layui-input-inline">
                <div id="demo1" style=" width: 100%;" style="max-height:30px;"></div>
            </div>
        </div>

        <div>
            <label class="layui-form-label" style="width: auto;margin:10px 5px 0 10px;">@T("进度筛选")：</label>
            <div class="layui-input-inline Select" style="width: auto;margin-top:10px;">
                <select name="progress" id="progress" lay-filter="progress">
                    <option value="-1">@T("全部")</option>
                    <option value="0">@T("待领料")</option>
                    <option value="1">@T("待生产")</option>
                    <option value="2">@T("生产中")</option>
                    <option value="3">@T("待打包")</option>
                    <option value="6">@T("已打包")</option>
                    <option value="4">@T("已出库")</option>
                    <option value="5">@T("已超时")</option>
                </select>
            </div>
        </div>

        <div>
            <label class="layui-form-label" style="width: auto;margin:10px 5px 10px 10px;">@T("状态筛选")：</label>
            <div class="layui-input-inline Select" style="width: auto;margin-top:10px;">
                <select name="status" id="status" lay-filter="status">
                    <option value="-1">@T("全部")</option>
                    <option value="0">@T("正常")</option>
                    <option value="1">@T("已取消")</option>
                </select>
            </div>
        </div>

        <div>
            <label class="layui-form-label" style="width: auto;margin:10px 5px 10px 10px;">@T("备注状态")：</label>
            <div class="layui-input-inline Select" style="width: auto;margin-top:10px;">
                <select name="hasRemark" id="hasRemark" lay-filter="hasRemark">
                    <option value="-1">@T("全部")</option>
                    <option value="0">@T("无备注")</option>
                    <option value="1">@T("有备注")</option>
                </select>
            </div>
        </div>
    </div>
</form>

<div class="layui-card">
    <div class="layui-card-body">
        <table class="layui-hide" id="tablist" lay-filter="tool"></table>
    </div>
</div>

<script asp-location="Footer">
    // 设置变量保存选中行信息
    let ids = new Array();
    // 保存当前页全部数据id，点击全选时使用
    let tableIds = new Array();

    $("body").on("click", function (event) {
        $("div[dtree-id][dtree-select]").removeClass("layui-form-selected");
        $("div[dtree-id][dtree-card]").removeClass("dtree-select-show layui-anim layui-anim-upbit");
    });

    layui.use(['table', 'abp', 'dg', 'form', 'dgcommon', 'dtree'], function () {
        var $ = layui.jquery;
        var abp = layui.abp;
        var form = layui.form;
        var table = layui.table;
        var dg = layui.dg;
        var os = layui.dgcommon;
        var dtree = layui.dtree;
        var laydate = layui.laydate;
        // 日期范围 - 左右面板独立选择模式
        laydate.render({
            elem: '#ID-laydate-range',
            range: ['#start', '#end'],
            type: 'datetime',       // 设置日期选择类型为年月
            format: 'yyyy-MM-dd HH:mm:ss',   // 设置日期的格式，这里是年-月
            done: function (value, date) {
                $("#start").val(value.split(" - ")[0]);
                $("#end").val(value.split(" - ")[1]);
                checkDateValidity();
            },
            choose: function (date) {
                console.log(date);
                // 用户选择日期的回调函数
                // 在这里可以处理用户选择日期后的逻辑
                laydate.close(); // 关闭日期选择器弹窗
            }
        });
        // 获取当前年份和日期
        var currentYear = new Date().getFullYear();
        var currentDate = new Date().toISOString().split('T')[0];

        table.render({
            elem: '#tablist'
            , url: '@Url.Action("GetList")'
            , page: true //开启分页
            , toolbar: '#user-toolbar'
            , defaultToolbar: [{
                title: '@T("刷新")',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print']
            , cellMinWidth: 60
            , cols: [[
                { type: 'checkbox', width: 60 }
                , { field: 'Id', title: '@T("编号")', width: 80 }
                , { field: 'OrderId', title: '@T("订单编号")', width: 140 }
                , { field: 'OrderingUser', title: '@T("打单人")', minWidth: 100 }
                , {
                    title: '@T("打单时间")', width: 160, templet: (d) => {
                        if (d.OrderingTime != undefined && d.OrderingTime[0] != 0) {
                            return `<div>${d.OrderingTime}</div>`
                        }
                        return `<div></div>`
                    }
                }
                , { field: 'IsDuplicate', title: '@T("重复打单")', width: 100 }
                , { field: 'PickingUser', title: '@T("领料人")', minWidth: 100 }
                , {
                    title: '@T("领料时间")', width: 160, templet: (d) => {
                        if (d.PickingTime != undefined && d.PickingTime[0] != 0) {
                            return `<div>${d.PickingTime}</div>`
                        }
                        return `<div></div>`
                    }
                }
                , { field: 'ProductionUser', title: '@T("生产者")', width: 100 }
                , {
                    title: '@T("生产时间")', width: 160, templet: (d) => {
                        if (d.ProductionTime != undefined && d.ProductionTime[0] != 0) {
                            return `<div>${d.ProductionTime}</div>`
                        }
                        return `<div></div>`
                    }
                }
                , { field: 'AuditingUser', title: '@T("审核人")', width: 100 }
                , {
                    title: '@T("审核时间")', width: 160, templet: (d) => {
                        if (d.AuditingTime != undefined && d.AuditingTime[0] != 0) {
                            return `<div>${d.AuditingTime}</div>`
                        }
                        return `<div></div>`
                    }
                }
                , { field: 'ShippingUser', title: '@T("打包人")', width: 100 }
                , {
                    title: '@T("打包时间")', width: 160, templet: (d) => {
                        if (d.ShippingTime != undefined && d.ShippingTime[0] != 0) {
                            return `<div>${d.ShippingTime}</div>`
                        }
                        return `<div></div>`
                    }
                }
                , { field: 'PackUser', title: '@T("出货人")', width: 100 }
                , {
                    title: '@T("出货时间")', width: 160, templet: (d) => {
                        if (d.PackTime != undefined && d.PackTime[0] != 0) {
                            return `<div>${d.PackTime}</div>`
                        }
                        return `<div></div>`
                    }
                }
                , { field: 'SpecificState', title: '@T("订单状态")', width: 100 }
                , { fixed: 'right', title: ' @T("操作")', toolbar: '#tool', width: 330 }
            ]]
            , limit: 13
            , limits: [10, 13, 20, 30, 50, 100]
            , height: 'full-100'
            , id: 'tables'
            , done: function (res) {
                // 设置当前页全部数据Id到全局变量
                tableIds = res.data.map(function (value) {
                    return value.Id;
                });

                // 设置当前页选中项
                $.each(res.data, function (idx, val) {
                    if (ids.indexOf(val.Id) > -1) {
                        val["LAY_CHECKED"] = 'true';
                        //找到对应数据改变勾选样式，呈现出选中效果
                        let index = val['LAY_INDEX'];
                        $('tr[data-index=' + index + '] input[type="checkbox"]').click();
                        form.render('checkbox'); //刷新checkbox选择框渲染
                    }
                });
                // 获取表格勾选状态，全选中时设置全选框选中
                let checkStatus = table.checkStatus('tables');
                if (checkStatus.isAll) {
                    $('.layui-table-header th[data-field="0"] input[type="checkbox"]').prop('checked', true);
                    form.render('checkbox'); //刷新checkbox选择框渲染
                }

            },
        });

        // 监听勾选事件
        table.on('checkbox(tool)', function (obj) {
            if (obj.checked == true) {
                if (obj.type == 'one') {
                    ids.push(obj.data.Id);
                } else {
                    for (let i = 0; i < tableIds.length; i++) {
                        //当全选之前选中了部分行进行判断，避免重复
                        if (ids.indexOf(tableIds[i]) == -1) {
                            ids.push(tableIds[i]);
                        }
                    }
                }
            } else {
                if (obj.type == 'one') {
                    let i = ids.length;
                    while (i--) {
                        if (ids[i] == obj.data.Id) {
                            ids.splice(i, 1);
                        }
                    }
                } else {
                    let i = ids.length;
                    while (i--) {
                        if (tableIds.indexOf(ids[i]) != -1) {
                            ids.splice(i, 1);
                        }
                    }
                }
            }
        });

        let UId = undefined;
        window.active = {
            reload: function () {
                table.reload('tables', {
                    page: {
                        curr: 1
                    },
                    where: {
                        key: $("#key").val(),
                        start: $("#start").val(),
                        end: $("#end").val(),
                        progress: $("#progress").val(),
                        status: $("#status").val(),
                        hasRemark: $("#hasRemark").val(),
                        UId: UId === undefined ? '' : UId
                    }
                })
            }
        }

        form.on('select(progress)', function (data) {
            ids = [];
            active.reload('tables')
        });

        form.on('select(status)', function (data) {
            ids = [];
            active.reload('tables')
        });

        form.on('select(hasRemark)', function (data) {
            ids = [];
            active.reload('tables')
        });

        var demo1 = xmSelect.render({
            el: '#demo1',
            radio: true, //单选
            name: 'keyword',
            paging: true,  // 是否翻页
            pageSize: 10,  // 每页数量
            filterable: true,  // 搜索模式
            remoteSearch: true,  // 远程搜索
            clickClose: true,
            pageRemote: true,  // 分页
            disabled: false, // 设置禁用
            remoteMethod: function (val, cb, show, pageIndex) {  // 远程方法
                var obj = [];

                // 接口数据
                $.post('@Url.Action("SearchUser")', { keyword: val, page: pageIndex, }, function (res) {
                    // console.log('搜索后的数据',res);
                    if (res.success) {
                        if (res.data != null) {
                            if (res.extdata.data != null) {
                                demo1.setValue(res.extdata.data)// 传入一个-默认值-数组
                            }
                            cb(res.data, res.extdata.PageCount);
                        }
                    }
                    else {
                        cb(obj, 0);
                        os.error(res.data.msg);
                    }
                });

            },
            on: function (data) {  // 监听选择
                if (data.arr.length == 0) {
                    UId = undefined
                    ids = [];
                    setTimeout(() => {
                        active.reload('tables')
                    }, 200)
                    return;
                }
                if (data.arr.length > 0) {
                    UId = data.arr[0].value
                    ids = [];
                    setTimeout(() => {
                        active.reload('tables')
                    }, 200);
                }
            },
        });


        // 监听输入订单
        $("#key").on("input", function (e) {
            ids = [];
            active.reload('tables', {
                where: { "key": $("#key").val() },
            })
        });

        table.on('toolbar(tool)', function (obj) {
            let data = obj.config
            var that = this
            if (obj.event === 'add') {
                window.add(data);
            } else if (obj.event === 'refresh') {
                active.reload();
            } else if (obj.event === 'refresh1') {
                window.refresh(data);
            } else if (obj.event === 'exportall') {
                var loadIndex = layui.layer.load();
                // 创建表单
                var form = $('<form></form>').attr('action', '@Url.Action("ExportAll")').attr('method', 'POST');
                form.append($('<input>').attr('type', 'hidden').attr('name', 'ids').attr('value', ids.length <= 0 ? "" : ids.join(',')));
                form.append($('<input>').attr('type', 'hidden').attr('name', 'key').attr('value', $("#key").val()));
                form.append($('<input>').attr('type', 'hidden').attr('name', 'start').attr('value', $("#start").val()));
                form.append($('<input>').attr('type', 'hidden').attr('name', 'end').attr('value', $("#end").val()));
                form.append($('<input>').attr('type', 'hidden').attr('name', 'progress').attr('value', $("#progress").val()));
                form.append($('<input>').attr('type', 'hidden').attr('name', 'status').attr('value', $("#status").val()));
                form.append($('<input>').attr('type', 'hidden').attr('name', 'hasRemark').attr('value', $("#hasRemark").val()));
                form.append($('<input>').attr('type', 'hidden').attr('name', 'UId').attr('value', UId === undefined ? '' : UId));
                // 将表单添加到body并提交
                layui.layer.close(loadIndex);
                form.appendTo('body').submit().remove();
            }
        });
        table.on('tool(tool)', function (obj) {
            var data = obj.data;
            if (obj.event === 'del') {
                parent.layer.confirm('@T("确认删除吗")?', { btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function (index) {
                    $.post('@Url.Action("Delete")', { Id: data.Id }, function (data) {
                        if (data.success) {
                            abp.notify.success(data.msg);
                            active.reload();
                        } else {
                            abp.notify.warn(data.msg);
                        }
                    });
                    parent.layer.close(index);
                });
            } else if (obj.event === 'edit') {
                window.edit(data);
            } else if (obj.event === 'readLogs') {
                window.readLogs(data);
            } else if (obj.event === 'TestPush') {
                window.TestPush(data);
            } else if (obj.event === 'end') {
                window.end(data);
            } else if (obj.event === 'cancel') {
                window.cancel(data);
            } else if (obj.event === 'setting') {
                window.setting(data);
            }
        });

        window.saveCallback = function (data) {
            parent.layer.close(data.index);
            abp.notify.success(data.msg);
            table.reload("tables");
        }

        window.add = function (data) {
            top.layui.dg.popupRight({
                id: 'Add'
                , title: ' @T("新增设备")'
                , closeBtn: 1
                , area: ['580px']
                , success: function () {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Add")" frameborder="0" class="layadmin-iframe" name="iframe' + data.index + '"></iframe>');
                }
            });
        }

        window.refresh = function (data) {
            parent.layer.confirm('@T("确认刷新投屏吗")?', { btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function (index) {
                $.post('@Url.Action("Refresh")', { Id: data.Id }, function (data) {
                    if (data.success) {
                        abp.notify.success(data.msg);
                        active.reload();
                    } else {
                        abp.notify.warn(data.msg);
                    }
                });
                parent.layer.close(index);
            });
        }
        window.edit = function (data) {
            top.layui.dg.popupRight({
                id: 'Edit'
                , title: ' @T("编辑设备")'
                , closeBtn: 1
                , area: ['580px']
                , success: function (obj, index) {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Edit")' + abp.utils.formatString("?id={0}&dTime={1}", data.Id, data.OrderingTime) + '" frameborder="0" class="layadmin-iframe"></iframe>');
                }
            });
        }
        window.readLogs = function (data) {
            top.layui.dg.popupRight({
                id: 'readLogs'
                , title: ' @T("查看日志")'
                , closeBtn: 1
                , area: ['680px']
                , success: function (obj, index) {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Logs")' + abp.utils.formatString("?OrderId={0}&dTime={1}", data.OrderId, data.OrderingTime) + '" frameborder="0" class="layadmin-iframe"></iframe>');
                }
            });
        }

        window.TestPush = function (data) {
            top.layui.dg.popupRight({
                id: 'TestPush'
                , title: ' @T("调测推送")'
                , closeBtn: 1
                , area: ['580px']
                , success: function (obj, index) {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("TestPush")' + abp.utils.formatString("?OrderId={0}&dTime={1}", data.OrderId, data.OrderingTime) + '" frameborder="0" class="layadmin-iframe"></iframe>');
                }
            });
        }

        window.end = function (data) {
            parent.layer.confirm('@T("确认完结订单吗")?', { btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function (index) {
                $.post('@Url.Action("EndOrder")', { orderId: data.OrderId,dTime:data.OrderingTime }, function (data) {
                    if (data.success) {
                        abp.notify.success(data.msg);
                        active.reload();
                    } else {
                        abp.notify.warn(data.msg);
                    }
                });
                parent.layer.close(index);
            });
        }

        window.cancel = function (data) {
            parent.layer.confirm('@T("确认取消订单吗")?', { btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function (index) {
                $.post('@Url.Action("CancelOrder")', { orderId: data.OrderId,dTime:data.OrderingTime }, function (data) {
                    if (data.success) {
                        abp.notify.success(data.msg);
                        active.reload();
                    } else {
                        abp.notify.warn(data.msg);
                    }
                });
                parent.layer.close(index);
            });
        }

        window.setting = function (data) {
            // console.log(data,data.OrderingTime);
            top.layui.dg.popupRight({
                id: 'setting'
                , title: ' @T("设置")'
                , closeBtn: 1
                , area: ['580px']
                , success: function (obj, index) {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Settings")' + abp.utils.formatString("?OrderId={0}&dTime={1}", data.OrderId, data.OrderingTime) + '" frameborder="0" class="layadmin-iframe"></iframe>');
                }
            });
        
        }

        window.details = function (data) {
            top.layui.dg.popupRight({
                id: 'DeviceDetails'
                , title: ' @T("查看")' + '(' + data.Name + ')'
                , closeBtn: 1
                , area: ['780px']
                , success: function () {
                    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Details")' + abp.utils.formatString("?id={0}&dTime={1}", data.Id, data.OrderingTime) + '" frameborder="0" class="layadmin-iframe"></iframe>');
                }
            });
        }
        //时间插件
        var startDate = laydate.render({
            elem: '#start',
            btns: ['clear', "confirm"],//只显示清空和确定按钮
            type: 'datetime',       // 设置日期选择类型为年月
            format: 'yyyy-MM-dd HH:mm:ss',   // 设置日期的格式，这里是年-月
            value: currentYear + '-01-01 00:00:00', // 设置默认值为当年1月1日
            choose: function (date) {
                // 用户选择日期的回调函数
                // 在这里可以处理用户选择日期后的逻辑
                laydate.close(); // 关闭日期选择器弹窗
            }
        });

        var endDate = laydate.render({
            elem: '#end',
            btns: ["clear", "confirm"],
            type: 'datetime',       // 设置日期选择类型为年月
            format: 'yyyy-MM-dd HH:mm:ss',   // 设置日期的格式，这里是年-月
            value: currentDate + ' 23:59:59', // 设置默认值为当年1月1日
            choose: function (date) {
                console.log(date);
                // 用户选择日期的回调函数
                // 在这里可以处理用户选择日期后的逻辑
                laydate.close(); // 关闭日期选择器弹窗
            }
        });

        function checkDateValidity() {
            var startValue = $("#start").val();
            var endValue = $("#end").val();

            if (startValue && endValue) {

                // if (startValue.substr(0, 4) != endValue.substr(0, 4)) {
                //     os.warning('开始时间和结束时间必须在同一年，请重新选择。');
                //     $("#start").val(""); // 清空开始时间输入框
                //     $("#end").val("");   // 清空结束时间输入框
                //     return;
                // }

                //     console.log(   $("#start").val(),
                //         $("#end").val());
                // }
                ids = [];
                active.reload("tables")

            }
        }

        window.warning = function (msg) {
            os.warning(msg);
        }
    });
</script>

<script type="text/html" id="tool">
    @if (this.Has((PermissionFlags)16))
    {
                           <a class="pear-btn pear-btn-warming pear-btn-xs" lay-event="readLogs"> @T("日志")</a>
    }
    @if (this.Has((PermissionFlags)128))
    {
                           <a class="pear-btn pear-btn-danger pear-btn-xs" lay-event="TestPush"> @T("调测推送")</a>
    }
    @if (this.Has((PermissionFlags)256))
    {
        @:{{# if(!d.IsEnd) { }}
        @:  <a class="pear-btn pear-btn-danger pear-btn-xs" lay-event="end"> @T("完结")</a>
        @:{{# } }}
    }
    @if (this.Has((PermissionFlags)512))
    {
        @:{{# if(!d.IsEnd) { }}
        @:  <a class="pear-btn pear-btn-danger pear-btn-xs" lay-event="cancel"> @T("取消")</a>
        @:{{# } }}
    }
        @if (this.Has((PermissionFlags)1024))
    {
        @:{{# if(!d.IsEnd) { }}
        @:  <a class="pear-btn pear-btn-danger pear-btn-xs" lay-event="setting"> @T("设置")</a>
        @:{{# } }}
    }
</script>

<script type="text/html" id="online">
    {{# if(d.Online) { }}
      <i class="layui-icon layui-icon-ok" style="color: #16b777"></i>
    {{# } else { }}
      <i class="layui-icon layui-icon-close" style="color: #ff5722"></i>
    {{# } }}
</script>

<script type="text/html" id="user-toolbar">
    @* <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
        <i class="layui-icon layui-icon-add-1"></i>
        @T("新增")
    </button> *@
    @if (this.Has((PermissionFlags)64))
    {
                    <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="refresh1">
                        <i class="layui-icon layui-icon-refresh"></i>
            @T("投屏刷新")
                    </button>
    }
    @if (this.Has((PermissionFlags)32))
    {
                    <a class="pear-btn pear-btn-primary pear-btn-md" lay-event="exportall">
                        <i class="layui-icon layui-icon-export"></i>
            @T("导出")
                    </a>
    }

</script>