﻿using DG.QiNiu.Extensions;
using DG.Web.Framework;
using DH.AspNetCore.MVC.Filters;
using DH.Helpers;
using HlktechIoT.Dto;
using HlktechIoT.Entity;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NewLife;
using NewLife.Data;
using NewLife.Log;
using Pek;
using Pek.Configs;
using Pek.Helpers;
using Pek.Models;
using Pek.MVC;
using Pek.Swagger;
using Pek.Timing;
using System.Security.Claims;

namespace HlktechIoT.Controllers.Api;

/// <summary>
/// 通用接口
/// </summary>
[Produces("application/json")]
[CustomRoute(ApiVersions.V1)]
[Authorize("jwt")]
public class Http2Controller : ApiControllerBaseX {
    /// <summary>
    /// 搜索产品型号
    /// </summary>
    /// <param name="keyword">关键字</param>
    /// <param name="page">页码</param>
    /// <param name="Id">产品型号id</param>
    /// <returns></returns>
    [AllowAnonymous]
    [HttpPost("SearchProductType")]
    [ApiSignature]
    public IActionResult SearchProductType([FromForm] String keyword, [FromForm] Int32 page, [FromForm] Int32 Id)
    {
        var res = new DResult();

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true,
            Sort = ProductType._.Id,
            Desc = true,
        };

        res.data = ProductType.Search(0, keyword, true, DateTime.MinValue, DateTime.MinValue, "", pages).Select(e =>
        {
            return new
            {
                name = e.Name,
                value = e.Id,
                e.NeedSn,
                e.NeedMac,
                e.NeedMac1,
                e.PCBCycle,
                e.ShieldCycle,
                e.MainChipCycle,
            };
        });

        res.success = true;

        var model = ProductType.FindById(Id);

        if (model == null)
        {
            res.extdata = new { pages.PageCount };
        }
        else
        {
            res.extdata = new { pages.PageCount, data = new List<object>() { new { name = model.Name, value = model.Id, NeedSn = model.NeedSn, NeedMac = model.NeedMac, model.NeedMac1, model.PCBCycle, model.ShieldCycle, model.MainChipCycle } } };
        }

        return Json(res);
    }

    /// <summary>
    /// 提交生产反馈问题
    /// </summary>
    /// <param name="AccessId">平台商户号</param>
    /// <param name="NonceStr">随机字符串</param>
    /// <param name="TimeStamp">时间戳</param>
    /// <param name="Sign">签名</param>
    /// <param name="Lng">语言项</param>
    /// <param name="Id">唯一标识</param>
    /// <param name="data">问题</param>
    /// <returns></returns>
    [AllowAnonymous]
    [HttpPost("SaveFeedback")]
    [ApiSignature]
    public IActionResult SaveFeedback([FromHeader] String AccessId, [FromHeader] String NonceStr, [FromHeader] Int64? TimeStamp, [FromHeader] String? Sign, [FromHeader] String? Lng, [FromHeader] String Id, [FromForm] FeedbackDto data)
    {
        using var span = DefaultTracer.Instance?.NewSpan(nameof(SaveFeedback));

        var result = new DGResult();

        if (Id.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10000;
            result.Message = GetResource("请求标识不能为空", Lng);
            return result;
        }
        result.Id = Id;
        if (Sign.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10001;
            result.Message = GetResource("签名不能为空", Lng);
            return result;
        }
        if (AccessId.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10002;
            result.Message = GetResource("平台商户号不能为空", Lng);
            return result;
        }
        if (NonceStr.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10003;
            result.Message = GetResource("随机字符串不能为空", Lng);
            return result;
        }
        if (TimeStamp <= 0)
        {
            result.ErrCode = 10004;
            result.Message = GetResource("时间戳不正确", Lng);
            return result;
        }
        if (data == null)
        {
            result.ErrCode = 10005;
            result.Message = GetResource("请求参数不能为空", Lng);
            return result;
        }
        if (data.Title.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10006;
            result.Message = GetResource("问题标题不能为空", Lng);
            return result;
        }
        if (data.Content.IsNullOrWhiteSpace())
        {
            result.ErrCode = 10007;
            result.Message = GetResource("问题内容不能为空", Lng);
            return result;
        }
        if (data.ProductTypeId <= 0)
        {
            result.ErrCode = 10008;
            result.Message = GetResource("产品型号不能为空", Lng);
            return result;
        }
        var modelProductType = ProductType.FindById(data.ProductTypeId);
        if (modelProductType == null)
        {
            result.ErrCode = 10009;
            result.Message = GetResource("产品型号不存在", Lng);
            return result;
        }
        var modelOpenPlatform = OpenPlatform.FindByAccessId(AccessId);
        if (modelOpenPlatform == null)
        {
            result.ErrCode = 10010;
            result.Message = GetResource("平台商户号不存在", Lng);
            return result;
        }

        if (!modelOpenPlatform.Enabled)
        {
            result.ErrCode = 10011;
            result.Message = GetResource("该账号未启用", Lng);
            return result;
        }

        var dic = new Dictionary<string, string>
        {
            ["accessId"] = AccessId,
            ["sign"] = Sign,
            ["nonceStr"] = NonceStr,
            ["timeStamp"] = TimeStamp.SafeString()
        };

        var verify = CheckSignature.CheckSign(dic, modelOpenPlatform.AccessKey, out String retusnsignature);

        if (verify != 1)
        {
            result.ErrCode = 10012;
            result.Message = GetResource($"签名验证错误", Lng);
            return result;
        }

        var UId = DHWeb.Identity.GetValue(ClaimTypes.Sid).ToDGInt();
        var Name = DHWeb.Identity.GetValue(ClaimTypes.Name);
        var Ip = UserHost;

        var model = new ProductionFeedback
        {
            Name = data.Title,
            ProductTypeId = data.ProductTypeId,
            Content = data.Content,
            CreateUserID = UId,
            CreateUser = Name,
            CreateIP = Ip,
        };

        if (data.Images.Count > 0 || data.Videos.Count > 0)
        {
            var AccessKey = OssSetting.Current.QiNiu.AccessKey;
            var SecretKey = OssSetting.Current.QiNiu.SecretKey;
            var Bucket = OssSetting.Current.QiNiu.Bucket;
            var BasePath = OssSetting.Current.QiNiu.BasePath ?? "";

            if (AccessKey.IsNullOrWhiteSpace() || SecretKey.IsNullOrWhiteSpace() || Bucket.IsNullOrWhiteSpace())
            {
                result.ErrCode = 10013;
                result.Message = GetResource($"七牛云未初始化配置", Lng);
                return result;
            }

            List<String> Images = new();
            List<String> Videos = new();
            foreach (var item in data.Images)
            {
                if(item != null)
                {
                    var fileName = (item.FileName + UnixTime.ToTimestamp()).MD5();
                    using var ms = new MemoryStream();
                    item.CopyTo(ms);
                    byte[] fileBytes = ms.ToArray();
                    var res = QiniuCloud.UploadData(fileName, fileBytes);
                    if (res.Code != 200)
                    {
                        result.ErrCode = 10014;
                        result.Message = GetResource($"图片上传失败", Lng);
                        return result;
                    }
                    var fileUrl = Path.Combine(BasePath, fileName);
                    Images.Add(fileUrl);
                }
            }
            foreach (var item in data.Videos)
            {
                if (item != null)
                {
                    var fileName = (item.FileName + UnixTime.ToTimestamp()).MD5();
                    using var ms = new MemoryStream();
                    item.CopyTo(ms);
                    byte[] fileBytes = ms.ToArray();
                    var res = QiniuCloud.UploadData(fileName, fileBytes);
                    if (res.Code != 200)
                    {
                        result.ErrCode = 10015;
                        result.Message = GetResource($"视频上传失败", Lng);
                        return result;
                    }
                    var fileUrl = Path.Combine(BasePath, fileName);
                    Videos.Add(fileUrl);
                }
            }

            if (Images.Count > 0)
            {
                model.PicPath = String.Join(",", Images);
            }
            if (Videos.Count > 0)
            {
                model.VideoPath = String.Join(",", Videos);
            }
        }
        model.Insert();
        result.Code = StateCode.Ok;
        return result;
    }



    ///// <summary>
    ///// 存储Sn/DeviceName生产数据
    ///// </summary>
    ///// <param name="Id"></param>
    ///// <param name="Lng"></param>
    ///// <param name="DeviceName">设备DN</param>
    ///// <param name="ProjectKey">设备项目Id</param>
    ///// <param name="Mac">设备Mac地址</param>
    ///// <returns></returns>
    ////[AllowAnonymous]
    //[HttpPost("SaveProduction")]
    //[ApiSignature]
    //public IActionResult SaveProduction([FromHeader] String Id, [FromHeader] String Lng, [FromForm] String DeviceName, [FromForm] String ProjectKey, [FromForm] String Mac)
    //{
    //    using var span = DefaultTracer.Instance?.NewSpan(nameof(GetFiveDeviceInfo));

    //    var result = new DGResult();

    //    if (Id.IsNullOrWhiteSpace())
    //    {
    //        result.Message = GetResource("请求标识不能为空", Lng);
    //        return result;
    //    }
    //    result.Id = Id;

    //    if (DeviceName.IsNullOrWhiteSpace())
    //    {
    //        result.Message = GetResource("设备DN不能为空", Lng);
    //        return result;
    //    }

    //    if (ProjectKey.IsNullOrWhiteSpace())
    //    {
    //        result.Message = GetResource("项目Id不能为空", Lng);
    //        return result;
    //    }

    //    if (Mac.IsNullOrWhiteSpace())
    //    {
    //        result.Message = GetResource("设备Mac地址不能为空", Lng);
    //        return result;
    //    }

    //    var check = SnLogs.FindByProjectIdAndSn2(ProjectKey, DeviceName);
    //    if (check != null)
    //    {
    //        result.Message = DeviceName + GetResource("设备已生产", Lng);
    //        return result;
    //    }

    //    var UId = DHWeb.Identity.GetValue(ClaimTypes.Sid).ToDGInt();
    //    var Name = DHWeb.Identity.GetValue(ClaimTypes.Name);

    //    check = new SnLogs
    //    {
    //        ProjectId = ProjectKey,
    //        Sn = DeviceName,
    //        Mac = Mac,
    //        CreateUserID = UId,
    //        UpdateUserID = UId,
    //        CreateUser = Name,
    //        UpdateUser = Name,
    //    };
    //    check.Insert();

    //    result.Code = StateCode.Ok;
    //    return result;
    //}
}
