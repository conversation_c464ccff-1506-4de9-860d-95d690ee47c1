﻿@{
    Html.AppendTitleParts(T("生产厂家订单日志").Text);

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");

    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/components/dynamic-operation-column.js");

    // Css
    Html.AppendCssFileParts(ResourceLocation.Head, "~/css/components/dynamic-operation-column.css");
}
<style asp-location="true">
    .layui-table td, .layui-table th {
    border-right: none;
    }

    .layui-card-body {
    padding-bottom: 1px;
    }

    .layui-layer-title {
    background-color: #fff;
    }

    .dtree-nav-item {
    padding-left: 0;
    }

    .layui-table-tool {
    z-index: 1;
    }

    .online {
    color: 'mediumaquamarine' !important;
    }

    .unline {
    color: 'gray' !important;
    }
</style>
<script asp-location="Head">
    var pleaseSelect = '@T("请选择")';
    var layuiPrint = '@T("打印")';
    var layuiExport = '@T("导出")';
    var layuiFilterColumn = '@T("筛选列")';
    var layuiArticlePage = '@T("条/页")';
    var layuiTotal = '@T("总共")';
    var layuiBtn = '@T("确定")';
    var layuiGoPage = '@T("到第")';
    var layuiPrev = '@T("上一页")';
    var layuiNext = '@T("下一页")';
    var layuiNoData = '@T("无数据")';
    var layuiAsc = '@T("升序")';
    var layuiDesc = '@T("降序")';
</script>

<form class="layui-form dg-form" id="importForm" enctype="multipart/form-data">
    <div class="layui-form-item" id="search" style="margin-bottom: 3px;">
        <div class="layui-inline">
            <label class="layui-form-label" style="width: auto">@T("关键字")：</label>
            <div class="layui-input-inline">
                <input type="text" name="key" id="key" placeholder="@T("请输入SN/主Mac/副Mac")" autocomplete="off" class="layui-input" lay-filter="key">
            </div>
        </div>
        <div class="layui-inline" style="padding-top: 10px;">

            <label class="layui-form-label" style="width: auto;margin:0px 0px 0 10px;">@T("筛选时间")：</label>
            <div class="layui-inline" id="ID-laydate-range">
                <div class="layui-input-inline">
                    <input type="text" name="start" id="start" readonly placeholder="@T("开始时间")" autocomplete="off" class="layui-input">
                </div>

                <div class="layui-form-mid">-</div>
                <div class="layui-input-inline">
                    <input type="text" name="end" id="end" readonly placeholder="@T("结束时间")" autocomplete="off" class="layui-input">
                </div>
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label" style="width: auto">@T("是否已被消费")：</label>
            <div class="layui-input-inline">
                <select name="consumed" id="consumed" lay-filter="consumed">
                    <option value="-1">@T("请选择")</option>
                    <option value="1">@T("是")</option>
                    <option value="0">@T("否")</option>
                </select>
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label" style="width: auto">@T("是否确认已被消费")：</label>
            <div class="layui-input-inline">
                <select name="confirmConsumed" id="confirmConsumed" lay-filter="confirmConsumed">
                    <option value="-1">@T("请选择")</option>
                    <option value="1">@T("是")</option>
                    <option value="0">@T("否")</option>
                </select>
            </div>
        </div>
    </div>
    <input type="file" id="importFile" name="file" style="display: none;" accept=".xls,.xlsx" />
    <input type="hidden"  name="orderId" value="@Model.Id"/>
</form>

<div class="layui-card">
    <div class="layui-card-body">
        <table class="layui-hide" id="tablist" lay-filter="tool"></table>
    </div>
</div>

<script asp-location="Footer">
    $("body").on("click", function (event) {
    $("div[dtree-id][dtree-select]").removeClass("layui-form-selected");
    $("div[dtree-id][dtree-card]").removeClass("dtree-select-show layui-anim layui-anim-upbit");
    });

    layui.use(['table', 'abp', 'dg', 'form', 'dgcommon', 'dtree'], function () {
    var $ = layui.jquery;
    var abp = layui.abp;
    var form = layui.form;
    var table = layui.table;
    var dg = layui.dg;
    var os = layui.dgcommon;
    var dtree = layui.dtree;
    var laydate = layui.laydate;

    // 获取当前年份和日期
    var currentYear = new Date().getFullYear();
    var currentDate = new Date().toISOString().split('T')[0];

    //时间插件
    var startDate = laydate.render({
    elem: '#start',
    btns: ['clear', "confirm"],//只显示清空和确定按钮
    type: 'datetime',       // 设置日期选择类型为年月
    format: 'yyyy-MM-dd HH:mm:ss',   // 设置日期的格式，这里是年-月
    value: currentYear + '-01-01 00:00:00', // 设置默认值为当年1月1日
    choose: function (date) {
    // 用户选择日期的回调函数
    // 在这里可以处理用户选择日期后的逻辑
    laydate.close(); // 关闭日期选择器弹窗
    }
    });

    var endDate = laydate.render({
    elem: '#end',
    btns: ["clear", "confirm"],
    type: 'datetime',       // 设置日期选择类型为年月
    format: 'yyyy-MM-dd HH:mm:ss',   // 设置日期的格式，这里是年-月
    value: currentDate + ' 23:59:59', // 设置默认值为当年1月1日
    choose: function (date) {
    console.log(date);
    // 用户选择日期的回调函数
    // 在这里可以处理用户选择日期后的逻辑
    laydate.close(); // 关闭日期选择器弹窗
    }
    });

    // 日期范围 - 左右面板独立选择模式
    laydate.render({
    elem: '#ID-laydate-range',
    range: ['#start', '#end'],
    type: 'datetime',       // 设置日期选择类型为年月
    format: 'yyyy-MM-dd HH:mm:ss',   // 设置日期的格式，这里是年-月
    done: function (value, date) {
    $("#start").val(value.split(" - ")[0]);
    $("#end").val(value.split(" - ")[1]);
    checkDateValidity();
    },
    choose: function (date) {
    console.log(date);
    // 用户选择日期的回调函数
    // 在这里可以处理用户选择日期后的逻辑
    laydate.close(); // 关闭日期选择器弹窗
    }
    });


        // 使用通用动态操作列组件
        var operationButtons = [
            @if (this.Has((PermissionFlags)256))
            {
                @:{ 
                @:    text: '@T("设置")', 
                @:    event: 'setting', 
                @:    class: 'pear-btn pear-btn-success', 
                @:    condition: function(d) { return true; },
                @:    alwaysShow: true
                @:},
            }
        ];

        // 初始化动态操作列组件
        var operationColumnWidth = window.dynamicOperationColumn.init({
            buttons: operationButtons,
            tableId: 'tablist',
            debug: true  // 开启调试模式
        });


    table.render({
    elem: '#tablist'
    , url: '@Url.Action("Snlist")?'
    , page: true //开启分页
    , toolbar: '#user-toolbar'
    , defaultToolbar: [
    {
    title: '@T("刷新")',
    layEvent: 'refresh',
    icon: 'layui-icon-refresh',
    }, 'filter', 'print']
    , cellMinWidth: 80
    , cols: [[
    { field: 'Id', title: '@T("编号")', minWidth: 120 }
    , { field: 'Sn', title: '@T("产品SN")', minWidth: 200 }
    , { field: 'Mac', title: '@T("主Mac")', minWidth: 140 }
    , { field: 'Mac1', title: '@T("副Mac")', minWidth: 140 }
    , { field: 'ShorUrl', title: '@T("短链接")', minWidth: 100,templet:(d)=>{
           if(!d.ShorUrl){
               return '';
           }
           return `<a href="https://s.0ht.cn/${d.ShorUrl}" target="_blank">${d.ShorUrl}</a>`
       } }
    , { field: 'FilePath', title: '@T("文件")', minWidth: 260,templet:(d)=>{
        if(!d.FilePath){
            return '';
        }
        return `<a href="${d.FilePath}" target="_blank">${d.FilePath}</a>`
    } }
    , { field: 'DataInfo', title: '@T("上传数据")', minWidth: 140,templet:(d)=>{
        if(d.DataInfo != '' && d.DataInfo != null){
            return `<div class="noneHover" lay-event="readDetail_tool2" style="cursor:pointer;color:rgb(50,50,50)"> ${d.DataInfo}</div>`; // 禁止溢出显示
        }else{
            return '';
        }
    } }
    , { field: 'Content', title: '@T("内容")', minWidth: 140,templet:(d)=>{
        if(d.Content != '' && d.Content != null){
            return `<div class="noneHover" lay-event="readDetail_tool1" style="cursor:pointer;color:rgb(50,50,50)"> ${d.Content}</div>`; // 禁止溢出显示
        }else{
            return '';
        }
    } }
    , { field: 'IsRepetition', title: '@T("是否允许重复")', minWidth: 120,templet:(d)=>{
    if(d.IsRepetition && d.IsRepetition != 'null'){
    return '@T("是")'
    }else{
    return '@T("否")'
    }
    } }
    , { field: 'IsRepetitionDate', title: '@T("重复截止时间")', minWidth: 160,templet:(d)=>{
    if (d.IsRepetitionDate != undefined && d.IsRepetitionDate[0] != 0) {
    return `<div>${d.IsRepetitionDate}</div>`
    }
    return `<div></div>`
    } }
    , { field: 'IsConsumed', title: '@T("是否已被消费")', minWidth: 160,templet:(d)=>{
    if(d.IsConsumed && d.IsConsumed != 'null'){
    return '@T("是")'
    }else{
    return '@T("否")'
    }
    } }
    , { field: 'ConsumedTime', title: '@T("消费时间")', minWidth: 160,templet:(d)=>{
    if (d.ConsumedTime != undefined && d.ConsumedTime[0] != 0) {
    return `<div>${d.ConsumedTime}</div>`
    }
    return `<div></div>`
    } }
    , { field: 'IsConfirmConsumed', title: '@T("是否确认消费")', minWidth: 160,templet:(d)=>{
    if(d.IsConfirmConsumed && d.IsConfirmConsumed != 'null'){
    return '@T("是")'
    }else{
    return '@T("否")'
    }
    } }
    , { field: 'ConfirmConsumedTime', title: '@T("确认消费时间")', minWidth: 160,templet:(d)=>{
    if (d.ConfirmConsumedTime != undefined && d.ConfirmConsumedTime[0] != 0) {
    return `<div>${d.ConfirmConsumedTime}</div>`
    }
    return `<div></div>`
    } }
    , { field: 'IsValidate', title: '@T("是否生产校验")', minWidth: 160,templet:(d)=>{
    if(d.IsValidate && d.IsValidate != 'null'){
    return '@T("是")'
    }else{
    return '@T("否")'
    }
    } }
    , { field: 'PCBCycle', title: '@T("PCB周期")', minWidth: 160,templet:(d)=>{
    if(d.PCBCycle && d.PCBCycle != 'null'){
    return '@T("是")'
    }else{
    return '@T("否")'
    }
    } }
    , { field: 'ShieldCycle', title: '@T("屏蔽罩周期")', minWidth: 160,templet:(d)=>{
    if(d.ShieldCycle && d.ShieldCycle != 'null'){
    return '@T("是")'
    }else{
    return '@T("否")'
    }
    } }
    , { field: 'MainChipCycle', title: '@T("主芯片周期")', minWidth: 160,templet:(d)=>{
    if(d.MainChipCycle && d.MainChipCycle != 'null'){
    return '@T("是")'
    }else{
    return '@T("否")'
    }
    } }
    , { field: 'CreateTime', title: '@T("创建时间")', minWidth: 160 }
    , { fixed: 'right', title: ' @T("操作")', toolbar: '#tool', width: operationColumnWidth }
    ]]
    , limit: 13
    , limits: [10, 13, 20, 30, 50, 100]
    , height: 'full-100'
    , id: 'tables'
    , where:{
    orderId:'@Model.Id',
    start:$('#start').val(),
    end:$('#end').val(),
    }
    , done: function (res) {
        // 使用通用组件应用操作列宽度
        window.dynamicOperationColumn.delayApplyWidth(100, true);
    }
    });



    function checkDateValidity() {
    var startValue = $("#start").val();
    var endValue = $("#end").val();

    if (startValue && endValue) {
    ids = [];
    active.reload("tables")

    }
    }

    window.active = {
    reload: function () {
    table.reload('tables',
    {
    where: {
    orderId: '@Model.Id',
    start:$('#start').val(),
    key:$('#key').val(),
    end:$('#end').val(),
    isConsumed:$('#consumed').val(),
    isConfirmConsumed:$('#confirmConsumed').val(),
    }
    });
    }
    }

    form.on('select(consumed)', function (data) {
    active.reload('tables')
    });
    form.on('select(confirmConsumed)', function (data) {
    active.reload('tables')
    });
    $("#key").on("input", function (e) {
    active.reload();
    });

    dtree.on('node("demoTree2")', function (obj) {
    table.reload('tables', {
    where: { "dId": obj.param.nodeId },
    page: {
    curr: 1
    }
    })
    });


            window.readDetail_tool2 =  (data) => {
                // 在此处输入 layer 的任意代码
                layer.open({
                    type: 1, // page 层类型
                    area: ['720px', '600px'],
                    title: '@T("查看详情")',
                    shade: 0.1, // 遮罩透明度
                    shadeClose: true, // 点击遮罩区域，关闭弹层
                    maxmin: true, // 允许全屏最小化
                    anim: 0, // 0-6 的动画形式，-1 不开启
                    content: `<div style="max-width:700px;margin:10px;word-wrap: break-word;line-height:20px;" id="copyText"> ${data.DataInfo} </div>`,
                    btn:['@T("一键复制")'],
                    yes: function (index, layero, that) {
                        var text = $("#copyText").text();
                        if (navigator.clipboard) {
                            navigator.clipboard.writeText(text);
                        } else {
                            const oInput = document.createElement('input');
                            oInput.value = text;
                            document.body.appendChild(oInput);
                            oInput.select(); // 选择对象
                            document.execCommand("Copy"); // 执行浏览器复制命令
                            document.body.removeChild(oInput);
                        }
                        layer.msg('复制成功');
                        // layer.close(index);
                    },
                });
            }

                        window.readDetail_tool1 =  (data) => {
                // 在此处输入 layer 的任意代码
                layer.open({
                    type: 1, // page 层类型
                    area: ['720px', '600px'],
                    title: '@T("查看详情")',
                    shade: 0.1, // 遮罩透明度
                    shadeClose: true, // 点击遮罩区域，关闭弹层
                    maxmin: true, // 允许全屏最小化
                    anim: 0, // 0-6 的动画形式，-1 不开启
                    content: `<div style="max-width:700px;margin:10px;word-wrap: break-word;line-height:20px;" id="copyText2"> ${data.Content} </div>`,
                    btn:['@T("一键复制")'],
                    yes: function (index, layero, that) {
                        var text = $("#copyText2").text();
                        if (navigator.clipboard) {
                            navigator.clipboard.writeText(text);
                        } else {
                            const oInput = document.createElement('input');
                            oInput.value = text;
                            document.body.appendChild(oInput);
                            oInput.select(); // 选择对象
                            document.execCommand("Copy"); // 执行浏览器复制命令
                            document.body.removeChild(oInput);
                        }
                        layer.msg('复制成功');
                        // layer.close(index);
                    },
                });
            }

    table.on('toolbar(tool)', function (obj) {
    let data = obj.config
    if (obj.event === 'add') {
    window.add(data);
    } else if (obj.event === 'refresh') {
    active.reload();
    }else if (obj.event === 'import') {
    window.import(data);
    }
    });

    table.on('tool(tool)', function (obj) {
    var data = obj.data;
    if (obj.event === 'del') {
    parent.layer.confirm('@T("确认删除吗")?', { btn: ["@T("确认")", "@T("取消")"], title: "@T("提示")" }, function (index) {
    $.post('@Url.Action("LogDelete")', { Id: data.Id }, function (data) {
    if (data.success) {
    abp.notify.success(data.msg);
    active.reload();
    } else {
    abp.notify.warn(data.msg);
    }
    });
    parent.layer.close(index);
    });
    } else if (obj.event === 'edit') {
    window.edit(data);
    } else if (obj.event === 'setting') {
    window.setting(data);
    }else if (obj.event === 'readDetail_tool2') {
    window.readDetail_tool2(data);
    }else if (obj.event === 'readDetail_tool1') {
    window.readDetail_tool1(data);
    }
    });

    window.setting = function (data) {
    window.settingPageIndex = top.layui.dg.popupRight({
    id: 'SettingSnRepeat'
    , title: ' @T("设置生产Sn")'
    , closeBtn: 1
    , area: ['580px']
    , success: function (obj, index) {
    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("SettingSnRepeat")' + abp.utils.formatString("?Id={0}", data.Id) + '" frameborder="0" class="layadmin-iframe"></iframe>');
    }
    });
    window.name = 'SnView';
    }

    window.import = function(data){
    window.importPageIndex = top.layui.dg.popupRight({
    id: 'Import'
    , title: ' @T("导入")'
    , closeBtn: 1
    , area: ['580px']
    , success: function (obj, index) {
    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Import")' + abp.utils.formatString("?orderId={0}", '@Model.Id') + '" frameborder="0" class="layadmin-iframe"></iframe>');
    }
    });
    window.name = 'SnView';
    }
    window.saveCallback = function (data) {
    parent.layer.close(data.index);
    abp.notify.success(data.msg);
    table.reload("tables");
    }

    window.add = function (data) {
    top.layui.dg.popupRight({
    id: 'Add'
    , title: ' @T("新增设备")'
    , closeBtn: 1
    , area: ['580px']
    , success: function () {
    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Add")" frameborder="0" class="layadmin-iframe" name="iframe' + data.index + '"></iframe>');
    }
    });
    }
    window.edit = function (data) {
    top.layui.dg.popupRight({
    id: 'Edit'
    , title: ' @T("编辑设备")'
    , closeBtn: 1
    , area: ['580px']
    , success: function (obj, index) {
    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Edit")' + abp.utils.formatString("?id={0}", data.Id) + '" frameborder="0" class="layadmin-iframe"></iframe>');
    }
    });
    }

    window.details = function (data) {
    top.layui.dg.popupRight({
    id: 'DeviceDetails'
    , title: ' @T("查看")' + '(' + data.Name + ')'
    , closeBtn: 1
    , area: ['780px']
    , success: function () {
    $('#' + this.id, window.parent.document).html('<iframe src="@Url.Action("Details")' + abp.utils.formatString("?id={0}", data.Id) + '" frameborder="0" class="layadmin-iframe"></iframe>');
    }
    });
    }

    window.warning = function (msg) {
    os.warning(msg);
    }
    $("#importFile").on("change", function () {
    var formData = new FormData($("#importForm")[0]);

    // 弹出模态对话框，内容为进度条
    var index = layer.open({
    type: 1,
    title: '@T("文件上传中")',
    closeBtn: 0, // 不显示关闭按钮
    shadeClose: false, // 不允许点击遮罩关闭
    shade: 0.5, // 遮罩透明度
    area: ['400px', '110px'], // 宽高
    content: '<div class="layui-progress layui-progress-big" lay-showPercent="true" lay-filter="uploadProgress" style="margin: 20px;"><div class="layui-progress-bar layui-bg-blue" lay-percent="0%"></div></div>'
    });

    // 初始化进度条
    layui.element.progress('uploadProgress', '0%');

    $.ajax({
    url: '@Url.Action("Import")',
    type: 'POST',
    data: formData,
    contentType: false,
    processData: false,
    xhr: function () {
    var xhr = new XMLHttpRequest();
    // 上传进度
    xhr.upload.addEventListener('progress', function (evt) {
    if (evt.lengthComputable) {
    var percentComplete = Math.floor((evt.loaded / evt.total) * 50); // 上传阶段最大50%
    layui.element.progress('uploadProgress', percentComplete + '%');
    }
    }, false);

    // 上传完成，进度条设为50%
    layui.element.progress('uploadProgress', '50%');

    // **修改弹出框标题为“文件解析中”**
    layer.title('@T("文件解析中")', index);

    // 模拟文件解析进度
    var parseProgress = 50;
    var parseInterval = setInterval(function () {
    if (parseProgress < 99) {
    parseProgress++;
    layui.element.progress('uploadProgress', parseProgress + '%');
    } else {
    clearInterval(parseInterval);
    // 解析完成，进度设为100%
    layui.element.progress('uploadProgress', '100%');
    }
    }, 1000); // 每1000ms增加1%

    return xhr;
    },
    success: function (res) {
    if (res.success) {
    // 提示成功信息
    abp.notify.success(res.msg);
    active.reload();
    // 关闭弹出框
    setTimeout(function () {
    layer.close(index);
    }, 500);

    } else {
    abp.notify.warn(res.msg);
    // 关闭弹出框
    setTimeout(function () {
    layer.close(index);
    }, 500);
    }
    $('#importFile').val('');
    },
    error: function () {
    // 请求错误，关闭弹出框
    layer.close(index);
    abp.notify.error('@T("文件上传失败")');

    // 重置文件输入控件的值
    $('#importFile').val('');
    }
    });
    });
    });
</script>
<script type="text/html" id="tool">
    <div class="operation-column">
        {{#  layui.each(window.operationButtons, function(index, button){ }}
            {{#  var isEnabled = button.condition(d); }}
            {{#  var buttonClass = button.class + ' pear-btn-xs'; }}
            {{#  if(!isEnabled){ }}
                {{#  buttonClass += ' disabled-button'; }}
            {{#  } }}
            <a class="{{buttonClass}}" lay-event="{{isEnabled ? button.event : 'disabled'}}"
               title="{{!isEnabled ? '当前状态下不可操作' : ''}}"
               data-enabled="{{isEnabled}}">{{button.text}}</a>
        {{#  }); }}
    </div>
</script>

<script type="text/html" id="user-toolbar" >
    @if (this.Has((PermissionFlags)4) && Model.Status == 2 && Model is ProductOrders)
    {
            <a class="pear-btn pear-btn-success pear-btn-xs" lay-event="import"> @T("导入")</a>
    }
</script>
