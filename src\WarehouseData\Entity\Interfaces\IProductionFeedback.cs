﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using System.Xml.Serialization;

namespace HlktechIoT.Entity;

/// <summary>生产反馈问题</summary>
public partial interface IProductionFeedback
{
    #region 属性
    /// <summary>编号</summary>
    Int32 Id { get; set; }

    /// <summary>问题标题</summary>
    String Name { get; set; }

    /// <summary>问题内容</summary>
    String? Content { get; set; }

    /// <summary>图片。多个图片用,分隔</summary>
    String? PicPath { get; set; }

    /// <summary>视频。多个视频用,分隔</summary>
    String? VideoPath { get; set; }

    /// <summary>状态。0为待处理，1为处理中，2为已处理，3为不予处理</summary>
    Int16 Status { get; set; }

    /// <summary>产品型号编号</summary>
    Int32 ProductTypeId { get; set; }

    /// <summary>处理原因</summary>
    String? Cause { get; set; }

    /// <summary>创建者</summary>
    String? CreateUser { get; set; }

    /// <summary>创建者</summary>
    Int32 CreateUserID { get; set; }

    /// <summary>创建时间</summary>
    DateTime CreateTime { get; set; }

    /// <summary>创建地址</summary>
    String? CreateIP { get; set; }
    #endregion
}
