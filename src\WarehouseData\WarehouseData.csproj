﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
	<LangVersion>latest</LangVersion>
    <TargetFramework>net9.0</TargetFramework>
	<RootNamespace>HlktechIoT</RootNamespace>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>3ca1bda5-d069-49b9-b90b-c19f8857a48b</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
	<DockerfileContext>..\..</DockerfileContext>
	<AssemblyTitle>海凌科仓库发货系统</AssemblyTitle>
    <Description>基于DH.Web.FrameWork制作的海凌科仓库发货系统</Description>
	<Authors>丁川</Authors>
    <Company>深圳市海凌科电子有限公司</Company>
    <Copyright>版权所有(C) 深圳市海凌科电子有限公司 2009-2025</Copyright>
	<Version>1.0.2024.0422</Version>
	<FileVersion>1.0.2024.0422</FileVersion>
	<AssemblyVersion>1.0.*</AssemblyVersion>
	<Deterministic>false</Deterministic>
	<OutputPath>..\..\Bin\WarehouseData</OutputPath>
	<DebugType>pdbonly</DebugType>
	<Optimize>true</Optimize>
	<DefineConstants>TRACE</DefineConstants>
	<GenerateRuntimeConfigDevFile>true</GenerateRuntimeConfigDevFile>
	<SatelliteResourceLanguages>en</SatelliteResourceLanguages>
	<!--允许你指定要在生成和发布过程中为哪些语言保留附属资源程序集-->
	  
	<!--混合打包，如启用则下面的混合打包规则也要启用-->
	<!--<RazorCompileOnBuild>true</RazorCompileOnBuild>
    <RazorCompileOnPublish>true</RazorCompileOnPublish>
    <PreserveCompilationContext>true</PreserveCompilationContext>
    <PreserveCompilationReferences>true</PreserveCompilationReferences>-->
	  
	<!--不打包模板文件-->
    <RazorCompileOnBuild>false</RazorCompileOnBuild>
    <MvcRazorCompileOnPublish>false</MvcRazorCompileOnPublish>

	<!--将此参数设置为true以获取从NuGet缓存复制到项目输出的dll。-->
	<CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>

	<NoWarn>$(NoWarn);1591</NoWarn>
	<GenerateDocumentationFile>true</GenerateDocumentationFile>
  </PropertyGroup>
	
  <PropertyGroup Condition="'$(Configuration)'=='Debug'">
	<DefineConstants>$(DefineConstants);DEBUG</DefineConstants>
	<DebugType>full</DebugType>
	<DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
	
  <ItemGroup>
	<Content Update="wwwroot\**\*.*">
		<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	</Content>
  </ItemGroup>
	
  <!--打包模板文件夹规则，混合编译，部分预编译，部分动态编译，此处如没有设置规则，则动态编译、混合编译无效-->
  <!--<ItemGroup>
    <MvcRazorFilesToCompile Include="Views\**\*.cshtml;EmailTemplates\**\*.cshtml" Exclude="wwwroot\themes\**\*.cshtml;" />
  </ItemGroup>-->

  <ItemGroup>
	<!-- 我们复制整个\App_Data目录。 但是我们忽略了JSON文件和数据保护密钥  -->
	<Content Include="App_Data\**" CopyToPublishDirectory="PreserveNewest" Exclude="App_Data\*.json" />
	<Content Remove="App_Data\*.json" />
	<Content Remove="Plugins\**" />
	<Content Update="App_Data\DataProtectionKeys\*.xml" CopyToPublishDirectory="Never" />

    <Compile Remove="Entity\Config\**" />
    <Compile Remove="Entity\Log\**" />
    <Content Remove="Entity\Config\**" />
    <Content Remove="Entity\Log\**" />
    <EmbeddedResource Remove="Entity\Config\**" />
    <EmbeddedResource Remove="Entity\Log\**" />
    <None Remove="Entity\Config\**" />
    <None Remove="Entity\Log\**" />
	  
	<Compile Remove="Themes\**" />
	<Content Remove="Themes\**" />
	<EmbeddedResource Remove="Themes\**" />
	<None Remove="Plugins\**" />
	<None Remove="Themes\**" />
	  
	<Content Include="Themes\**" CopyToPublishDirectory="PreserveNewest" Exclude="Themes\**\*.config;Themes\**\*.cshtml;Themes\**\*.json" />
	<None Include="Themes\**" CopyToPublishDirectory="PreserveNewest" />
  </ItemGroup>

  <ItemGroup>
    <!--<PackageReference Include="DH.LettuceEncrypt" Version="3.6.2024.8150164" />-->
    <PackageReference Include="DG.QiNiu.Extensions" Version="8.9.2025.5160083" />
    <PackageReference Include="DH.Magicodes.IE.Excel" Version="4.0.2025.62300081" />
    <PackageReference Include="DH.MiniExcel" Version="4.1.2025.318-beta0914" />
    <PackageReference Include="DH.NAgent" Version="4.13.2025.701-beta0923" />
    <PackageReference Include="DH.NCode" Version="4.13.2025.701-beta0843" />
    <PackageReference Include="DH.NCore" Version="4.13.2025.701-beta0815" />
    <PackageReference Include="DH.NMQTT" Version="4.13.2025.701-beta1006" />
    <PackageReference Include="DH.Npoi" Version="4.0.2025.62300081" />
	<PackageReference Include="DH.NRedis.Extensions" Version="4.13.2025.701-beta0850" />
	<PackageReference Include="DH.Permissions" Version="4.0.2025.606-beta1258" />
    <PackageReference Include="DH.SignalR" Version="4.12.2025.626-beta1554" />
    <PackageReference Include="DH.SLazyCaptcha" Version="4.0.2025.703-beta0423" />
    <PackageReference Include="DH.Swagger" Version="4.13.2025.707-beta0858" />
	<PackageReference Include="DH.AspNetCore.ServerSentEvents" Version="4.0.2025.62300081" />
	<PackageReference Include="HlktechIoT.UICube" Version="1.7.2025.7050239" />
	<PackageReference Include="iTextSharp" Version="5.5.13.4" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.22.1" />
    <PackageReference Include="Pek.AspNetCore" Version="4.12.2025.624-beta0913" />
    <PackageReference Include="Pek.Common" Version="4.12.2025.626-beta1317" />
    <PackageReference Include="SixLabors.ImageSharp.Drawing" Version="2.1.6" />
	<!--<PackageReference Include="YRY.Api.Framework" Version="0.3.2024.1080051">
      <CopyToOutputDirectory>lib\net8.0\*.xml</CopyToOutputDirectory>		
	</PackageReference>-->
  </ItemGroup>
	
  <ItemGroup>
	<!-- 此设置解决了vs2019中websdk中此更新引起的问题
    https://github.com/aspnet/websdk/commit/7e6b193ddcf1eec5c0a88a9748c626775555273e#diff-edf5a48ed0d4aa5a4289cb857bf46a04
    因此，我们恢复了标准配置行为（没有副本到输出目录）
     为了避免在发布过程中出现“ Duplicate dll”错误。
     我们还可以根据以下条件使用“ ExcludeConfigFilesFromBuildOutput” https://github.com/aspnet/AspNetCore/issues/14017 -->
	<Content Update="**\*.config;**\*.json" CopyToOutputDirectory="Never" CopyToPublishDirectory="PreserveNewest" />
  </ItemGroup>
	
  <ItemGroup>
	<Content Update="Settings\*.*">
		<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	</Content>
	<Content Update="appsettings.json">
	  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	</Content>
	<Content Update="web.config">
	  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	</Content>
  </ItemGroup>
	
  <ItemGroup>
	<PackageReference Update="Microsoft.SourceLink.GitHub" Version="8.0.0" />
  </ItemGroup>
	
  <!--设置构建后从NuGet包中复制XML文档-->
  <Target Name="AfterTargetsBuild" AfterTargets="Build">
      <ItemGroup>
             <PackageReferenceFiles Condition="%(PackageReference.CopyToOutputDirectory) != ''" Include="$(NugetPackageRoot)\%(PackageReference.Identity)\%(PackageReference.Version)\%(PackageReference.CopyToOutputDirectory)" />
      </ItemGroup>
      <Copy SourceFiles="@(PackageReferenceFiles)" DestinationFolder="$(OutDir)" />
  </Target>
	
  <!--设置发布后从NuGet包中复制XML文档-->
  <Target Name="AfterTargetsPublish" AfterTargets="Publish">
      <ItemGroup>
             <PackageReferenceFiles Condition="%(PackageReference.CopyToOutputDirectory) != ''" Include="$(NugetPackageRoot)\%(PackageReference.Identity)\%(PackageReference.Version)\%(PackageReference.CopyToOutputDirectory)" />
      </ItemGroup>
      <Copy SourceFiles="@(PackageReferenceFiles)" DestinationFolder="$(PublishDir)" />
  </Target>
	
  <!--DATAS 是一项很棒的新功能，它将 Workstation GC 和 Server GC 的优势结合在一起：您开始时内存更少，当请求激增时，GC 可以动态扩展其托管堆的数量以提高吞吐量。当请求数在以后的某个时间点减少时，也可以减少托管堆的数量以释放内存 https://mp.weixin.qq.com/s/WJEkHmV3bPYStqZoDJhvmg-->
  <PropertyGroup>
     <ServerGarbageCollection>true</ServerGarbageCollection>
     <GarbageCollectionAdapatationMode>1</GarbageCollectionAdapatationMode>
  </PropertyGroup>
	
	
  <PropertyGroup>
    <SpaRoot>..\warehousedata.client</SpaRoot>
    <SpaProxyLaunchCommand>npm run dev</SpaProxyLaunchCommand>
    <SpaProxyServerUrl>https://localhost:5173</SpaProxyServerUrl>
  </PropertyGroup>
	
  <ItemGroup>
    <ProjectReference Include="..\warehousedata.client\warehousedata.client.esproj">
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
    </ProjectReference>
	<PackageReference Include="Microsoft.AspNetCore.SpaProxy">
      <Version>9.0.6</Version>
    </PackageReference>
  </ItemGroup>
	
  <ItemGroup>
    <Folder Include="wwwroot\files\" />
  </ItemGroup>

</Project>
