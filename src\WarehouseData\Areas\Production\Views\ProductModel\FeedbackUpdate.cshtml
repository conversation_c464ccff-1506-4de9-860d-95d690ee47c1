﻿@using Pek.Configs
@{
    Html.AppendTitleParts(T("编辑产品型号问题反馈").Text);

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
}
<style asp-location="true">
    .layui-input-block {
        margin-right: 40px;
    }

    .containers {
        padding-top: 30px;
        padding-left: 30px;
    }

    .layui-form-item.btn {
        padding-top: 10px;
        padding-left: 115px;
    }

    .label-width span {
        color: #f00;
    }

    .layui-form-label {
        width: 94px;
        white-space:nowrap;
        min-width:110px;
    }

    .upload {
        cursor: pointer;
        max-width: 120px;
        max-height: 90px;
    }
    .select{
        max-width: 420px !important;
        border: 1px solid #e6e6e6;
    }
</style>
<script asp-location="Head">
    var closeThis = '@T("关 闭 当 前")';
    var closeOther = '@T("关 闭 其 他")';
    var closeAll = '@T("关 闭 全 部")';

    var jsMenuStyle = "@T("菜单风格")";
    var jsTopStyle = "@T("顶部风格")";
    var jsMenu = "@T("菜单")";
    var jsView = "@T("视图")";
    var jsBanner = "@T("通栏")";
    var jsThroughColor = "@T("通色")";
    var jsFooter = "@T("页脚")";
    var jsMoreSettings = "@T("更多设置")";
    var jsOpen = "@T("开")";
    var jsClose = "@T("关")";
    var jsThemeColor = "@T("主题配色")";
    var layuiNoData = '@T("无数据")';
    var layuiAsc = "@T("升序")";
    var layuiDesc = "@T("降序")";

    var layuiPrint = '@T("打印")';
    var layuiExport = '@T("导出")';
    var layuiFilterColumn = '@T("筛选列")';
    var layuiArticlePage = '@T("条/页")';
    var layuiTotal = '@T("总共")';
    var layuiBtn = '@T("确定")';
    var layuiGoPage = '@T("到第")';
    var layuiPrev = '@T("上一页")';
    var layuiNext = '@T("下一页")';
</script>

<div class="containers">
    <form class="layui-form">
        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("问题标题")</label>
            <div class="layui-input-inline" style="min-width:320px">
                <input type="text" name="Name" autocomplete="off" class="layui-input" lay-filter="Versions" value="@Model.Name">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("问题内容")</label>
            <div class="layui-input-inline" style="min-width:320px">
                <input type="text" name="Content" autocomplete="off" class="layui-input" lay-filter="Versions" value="@Model.Content">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("图片")</label>
            <div class="layui-input-inline" style="min-width:320px">
                <div id="Images">
                    @if (Model.PicPath != null & Model.PicPath != "")
                    {
                        @foreach (var item in Model.PicPath.Split(","))
                        {
                            <img src="@OssSetting.Current.QiNiu.Domain@item" width="100" data-url="@item">
                        } 
                    }
                </div>
                <div class="layui-upload-drag" id="upload1" style="width:100px">
                    <i class="layui-icon"></i>
                    <p>@T("点击上传，或将文件拖拽到此处")</p>
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("视频")</label>
            <div  class="layui-input-inline" style="min-width:320px">
                <div id="Videos">
                    @if (Model.VideoPath != null & Model.VideoPath != "")
                    {
                        @foreach (var item in Model.VideoPath.Split(","))
                        {
                            <video src="@OssSetting.Current.QiNiu.Domain@item" width="120" data-url="@item"></video>
                        }
                    }
                </div>
                <div class="layui-upload-drag" id="upload2" style="width:100px">
                    <i class="layui-icon"></i>
                    <p>@T("点击上传，或将文件拖拽到此处")</p>
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("状态")</label>
            <div class="layui-input-inline" style="min-width:320px">
                <select name="Status">
                    <!option value="0" @(Model.Status == 0 ? "selected":"")>@T("待处理")</!option>
                    <!option value="1" @(Model.Status == 1 ? "selected":"")>@T("处理中")</!option>
                    <!option value="2" @(Model.Status == 2 ? "selected":"")>@T("已处理")</!option>
                    <!option value="3" @(Model.Status == 3 ? "selected":"")>@T("不予处理")</!option>
                </select>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("处理原因")</label>
            <div class="layui-input-inline" style="min-width:320px">
                <input type="text" name="Cause" autocomplete="off" class="layui-input" lay-filter="Versions" value="@Model.Cause">
            </div>
        </div>

        <div class="layui-form-item btn">
            <input hidden name="Id" value="@Model.Id" /> 
            <input type="button" lay-submit lay-filter="Submit" id="Submit" class="pear-btn pear-btn-primary pear-btn-normal" style="margin-left:20px" value="@T("保存")" />
        </div>
    </form>
</div>

<script asp-location="Footer">
    layui.use(['abp', 'form', 'dgcommon', 'dg', 'upload'], function () {
        var $ = layui.$;
        var abp = layui.abp;
        var form = layui.form;
        var os = layui.dgcommon;
        var dg = layui.dg;
        var laydate = layui.laydate;
        var upload = layui.upload;

        upload.render({
            elem: '#upload1'
            , url: '@Url.Action("UploadFiles")' //改成您自己的上传接口
            , done: function (res) {
                if (!res.success) { //失败打印
                    os.warning(res.msg);
                    return;
                }
                os.success('@T("上传成功")');
                $('#Images').append('<img src="'+res.data.FileUrl+'" width="100" data-url="'+res.data.FilePath+'">');
            },
            before: function () {

            }
            , accept: 'file' //允许上传的文件类型
        });

        upload.render({
            elem: '#upload2'
            , url: '@Url.Action("UploadFiles")' //改成您自己的上传接口
            , done: function (res) {
                if (!res.success) { //失败打印
                    os.warning(res.msg);
                    return;
                }
                os.success('@T("上传成功")');
                $('#Videos').append('<video src="'+res.data.FileUrl+'" width="120" data-url="'+res.data.FilePath+'"></video>');
            },
            before: function () {

            }
            , accept: 'video' //允许上传的文件类型
        });

        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.iframeAuto(index);

        var parentList = window.parent
        var parentPage = null;

        // 第二步：拿到对应层
        for (let i = 0; i < parentList.length; i++) {
            if (parentList[i].name === 'Feedback') { //这里的name自己去对应层DIY
                parentPage = parentList[i]
                break;
            }
        }
        // 第三步：操作对应层
        var parent_window = parentPage.window  //获取父层的window层
        var parent_layer = parentPage.layer //获取父层的layer
        var parent_notify = parentPage.layui.abp.notify //获取父层的layui.notify --消息通知
        var parent_layui  = parentPage.layui  //获取父层的layui
        var currentPageCloseIndex = parent_window.editPageIndex //当前层的关闭index下标

        form.on('submit(Submit)', function (data) {
            // console.log(data);

            // 获取包含图片的容器
            const container = document.getElementById('Images');
            // 获取所有 img 标签
            const images = container.querySelectorAll('img');
            // 提取所有 data-url 的值
            const dataUrls = Array.from(images).map(img => img.getAttribute('data-url'));
            // 将所有 data-url 拼接成一个字符串，以逗号分隔
            const combinedDataUrls = dataUrls.join(',');

            data.field.PicPath = combinedDataUrls;

            // 获取包含视频的容器
            var containerVideo = document.getElementById('Videos');
            // 获取所有 video 标签
            const videos = containerVideo.querySelectorAll('video');
            // 提取所有 data-url 的值
            const dataUrlsVideo = Array.from(videos).map(video => video.getAttribute('data-url'));
            // 将所有 data-url 拼接成一个字符串，以逗号分隔
            const combinedDataUrlsVideo = dataUrlsVideo.join(',');

            data.field.VideoPath = combinedDataUrlsVideo;

            var waitIndex = parent.layer.load(2);

            abp.ajax({
                url: "@Url.Action("FeedbackUpdate")",
                contentType : "application/x-www-form-urlencoded; charset=utf-8",
                data: data.field,
                abpHandleError: false
            }).done(function (data) {
                if (!data.success) {
                    abp.notify.error(data.msg);
                    return false;
                }
                parent.layer.close(currentPageCloseIndex);
                parent_notify.success(data.msg);
                parentPage.active.reload();
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;
        });

    });
</Script>