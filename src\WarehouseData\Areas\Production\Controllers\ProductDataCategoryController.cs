﻿using DG.Web.Framework;

using HlktechIoT.Entity;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;

using Pek.Models;

using System.ComponentModel;

using XCode.Membership;

using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Production.Controllers;

    /// <summary>产品资料分类</summary>
    [DisplayName("产品资料分类")]
    [Description("产品资料分类")]
    [ProductionArea]
    [DHMenu(95, ParentMenuName = "ProductionsManager", CurrentMenuUrl = "~/{area}/ProductDataCategory", CurrentMenuName = "ProductDataCategoryList", LastUpdate = "20250613")]
    public class ProductDataCategoryController : BaseAdminControllerX
    {
        /// <summary>
        /// 产品资料分类列表
        /// </summary>
        /// <returns></returns>
        [DisplayName("列表")]
        [EntityAuthorize(PermissionFlags.Detail)]
        public IActionResult Index()
        {
            return View();
        }

        /// <summary>
        /// 产品资料分类列表
        /// </summary>
        /// <param name="ParentId">父级id</param>
        /// <param name="Name">分类名称</param>
        /// <param name="page">页码</param>
        /// <param name="limit">条数</param>
        /// <returns></returns>
        [DisplayName("列表")]
        [EntityAuthorize(PermissionFlags.Detail)]
        public IActionResult GetList(Int32 ParentId, String Name, Int32 page, Int32 limit)
        {
            var pages = new PageParameter
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "Id",
                Desc = true,
            };
            var data = ProductDataCategory.Search(Name, ParentId, null, DateTime.MinValue, DateTime.MinValue, "", pages);
            return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
        }

        /// <summary>
        /// 产品资料子分类列表
        /// </summary>
        /// <param name="Id">分类id</param>
        /// <returns></returns>
        [DisplayName("列表")]
        [EntityAuthorize(PermissionFlags.Detail)]
        public IActionResult ChildList(Int32 Id)
        {
            var model = ProductDataCategory.FindById(Id);
            if (model == null)
            {
                return Content(GetResource("数据不存在"));
            }
            return View(model);
        }

        /// <summary>
        /// 添加产品资料分类
        /// </summary>
        /// <returns></returns>
        [DisplayName("添加")]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult Add()
        {
            return View();
        }

        /// <summary>
        /// 添加产品资料子分类
        /// </summary>
        /// <param name="Id">父级id</param>
        /// <returns></returns>
        [DisplayName("添加")]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult ChildAdd(Int32 Id)
        {
            var model = ProductDataCategory.FindById(Id);
            if (model == null)
            {
                return Content(GetResource("数据不存在"));
            }
            return View(model);
        }

        /// <summary>
        /// 添加产品资料分类
        /// </summary>
        /// <param name="Name">名称</param>
        /// <param name="Status">状态</param>
        /// <param name="Remark">备注</param>
        /// <param name="ParentId">父级id</param>
        /// <param name="Must">是否必须</param>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("添加")]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult Add(String Name, Boolean Status, String Remark,Int32 ParentId, Boolean Must)
        {
            DResult res = new();
            if (Name.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("名称不能为空");
                return Json(res);
            }
            var model = ProductDataCategory.FindByName(Name);
            if (model != null)
            {
                res.msg = GetResource("名称已存在");
                return Json(res);
            }
            model = new ProductDataCategory
            {
                Name = Name,
                Status = Status,
                Must = Must,
                Remark = Remark,
                ParentId = ParentId,
            };
            model.Insert();
            res.success = true;
            res.msg = GetResource("添加成功");
            return Json(res);
        }

        /// <summary>
        /// 编辑产品资料分类列表
        /// </summary>
        /// <param name="Id">分类id</param>
        /// <returns></returns>
        [DisplayName("编辑")]
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult Update(Int32 Id)
        {
            var model = ProductDataCategory.FindById(Id);
            if(model == null)
            {
                return Content(GetResource("数据不存在"));
            }
            return View(model);
        }

        /// <summary>
        /// 编辑产品资料分类
        /// </summary>
        /// <param name="Name">名称</param>
        /// <param name="Status">状态</param>
        /// <param name="Remark">备注</param>
        /// <param name="Id">分类id</param>
        /// <param name="Must">是否必须</param>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("编辑")]
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult Update(String Name, Boolean Status, String Remark, Int32 Id, Boolean Must)
        {
            DResult res = new();
            if (Name.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("名称不能为空");
                return Json(res);
            }
            var model = ProductDataCategory.FindByName(Name);
            if (model != null && model.Id != Id)
            {
                res.msg = GetResource("名称已存在");
                return Json(res);
            }

        model = ProductDataCategory.FindById(Id);
        if (model == null)
        {
            res.msg = GetResource("数据不存在");
            return Json(res);
        }
        model.Name = Name;
        model.Status = Status;
        model.Remark = Remark;
        model.Must = Must;
        model.Update();
        res.success = true;
        res.msg = GetResource("编辑成功");
        return Json(res);
    }

        /// <summary>
        /// 编辑产品资料子分类
        /// </summary>
        /// <param name="Id">子分类id</param>
        /// <returns></returns>
        [DisplayName("编辑")]
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult ChildUpdate(Int32 Id)
        {
            var model = ProductDataCategory.FindById(Id);
            if (model == null)
            {
                return Content(GetResource("数据不存在"));
            }
            return View(model);
        }

        /// <summary>
        /// 修改产品资料分类启用状态
        /// </summary>
        /// <param name="Id">分类id</param>
        /// <param name="Status">状态</param>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("修改状态")]
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult ModifyState(Int32 Id, Boolean Status)
        {
            var result = new DResult();

        var model = ProductDataCategory.FindById(Id);
        if (model == null)
        {
            result.msg = GetResource("状态调整出错");
            return Json(result);
        }

        var list = ProductDataCategory.FindAllByParentId(Id);
        foreach (var item in list)
        {
            item.Status = Status;
            item.Update();
        }

        model.Status = Status;
        model.Update();

        result.success = true;
        result.msg = GetResource("状态调整成功");

        return Json(result);
    }

        /// <summary>
        /// 修改产品资料分类是否必须
        /// </summary>
        /// <param name="Id">分类id</param>
        /// <param name="Status">是否必须</param>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("修改状态")]
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult ModifyMust(Int32 Id, Boolean Status)
        {
            var result = new DResult();

        var model = ProductDataCategory.FindById(Id);
        if (model == null)
        {
            result.msg = GetResource("状态调整出错");
            return Json(result);
        }

        var list = ProductDataCategory.FindAllByParentId(Id);
        foreach (var item in list)
        {
            item.Status = Status;
            item.Update();
        }

        model.Must = Status;
        model.Update();

        result.success = true;
        result.msg = GetResource("状态调整成功");

        return Json(result);
    }

        /// <summary>
        /// 删除产品资料分类
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("删除")]
        [EntityAuthorize(PermissionFlags.Delete)]
        public IActionResult Delete(Int32 Id)
        {
            var res = new DResult();
            var count = ProductData.FindCount(ProductData._.ProductDataCategoryId2 == Id | ProductData._.ProductDataCategoryId1 == Id);
            if(count > 0)
            {
                res.msg = GetResource("该分类下有产品资料，不能删除");
                return Json(res);
            }
            ProductDataCategory.Delete(ProductDataCategory._.Id == Id);
            ProductDataCategory.Delete(ProductDataCategory._.ParentId == Id);
            res.success = true;
            res.msg = GetResource("删除成功");
            return Json(res);

    }
}
