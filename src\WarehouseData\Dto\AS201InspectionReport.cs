﻿namespace HlktechIoT.Dto;

/// <summary>
/// AS201质检报告数据传输对象
/// </summary>
public class AS201InspectionReport {
    /// <summary>
    /// 产品型号
    /// </summary>
    public String ProductModel { get; set; } = "HLK-AS201";

    /// <summary>
    /// 固件版本
    /// </summary>
    public String FirmwareVersion { get; set; } = "V1.0.0";

    /// <summary>
    /// 产品ID(SN)
    /// </summary>
    public String? SN { get; set; }

    /// <summary>
    /// 设备Mac地址
    /// </summary>
    public String? Mac { get; set; }

    /// <summary>
    /// 订单号
    /// </summary>
    public String? OrderId { get; set; }

    /// <summary>
    /// 检测转台
    /// </summary>
    public String? InspectionTurntable { get; set; }

    /// <summary>
    /// 检测人员
    /// </summary>
    public String? Inspectors { get; set; }

    /// <summary>
    /// 检测日期
    /// </summary>
    public DateTimeOffset DetectionDate { get; set; } = DateTimeOffset.Now;

    /// <summary>
    /// 零偏信息
    /// </summary>
    public AccelerationZeroBias? AccelerationZeroBias { get; set; }

    /// <summary>
    /// 标定信息
    /// </summary>
    public GyroscopeCalibrationPolynomial? GyroscopeCalibrationPolynomial { get; set; }

    /// <summary>
    /// 检测数据
    /// </summary>
    public List<AngleDetectionData>? AngleDetectionData { get; set; }

    /// <summary>
    /// 类型参数
    /// </summary>
    public TypeData? TypeData { get; set; }
}

/// <summary>
/// 零偏信息
/// </summary>
public class AccelerationZeroBias {
    /// <summary>
    /// X轴
    /// </summary>
    public Double X { get; set; }

    /// <summary>
    /// Y轴
    /// </summary>
    public Double Y { get; set; }

    /// <summary>
    /// Z轴
    /// </summary>
    public Double Z { get; set; }
}

/// <summary>
/// 标定信息
/// </summary>
public class GyroscopeCalibrationPolynomial {
    /// <summary>
    /// X轴
    /// </summary>
    public List<Double>? X { get; set; }

    /// <summary>
    /// Y轴
    /// </summary>
    public List<Double>? Y { get; set; }

    /// <summary>
    /// Z轴
    /// </summary>
    public List<Double>? Z { get; set; }
}

/// <summary>
/// 检测数据
/// </summary>
public class AngleDetectionData {
    /// <summary>
    /// 转台角度
    /// </summary>
    public Double TumtablesAngle { get; set; }

    /// <summary>
    /// X轴参考值
    /// </summary>
    public Double XReference { get; set; }
    /// <summary>
    /// X轴采集角度
    /// </summary>
    public Double AngleOfXaxis { get; set; }

    /// <summary>
    /// X轴偏差值
    /// </summary>
    public Double OffsetOfXaxis { get; set; }

    /// <summary>
    /// Y轴参考值
    /// </summary>
    public Double YReference { get; set; }

    /// <summary>
    /// Y轴采集角度
    /// </summary>
    public Double AngleOfYaxis { get; set; }

    /// <summary>
    /// Y轴偏差值
    /// </summary>
    public Double OffsetOfYaxis { get; set; }
}

/// <summary>
/// 类型参数
/// </summary>
public class TypeData
{
    /// <summary>
    /// PCB周期
    /// </summary>
    public String? PCBCycle { get; set; }

    /// <summary>
    /// 屏蔽罩周期
    /// </summary>
    public String? ShieldCycle { get; set; }

    /// <summary>
    /// 主芯片周期
    /// </summary>
    public String? MainChipCycle { get; set; }
}