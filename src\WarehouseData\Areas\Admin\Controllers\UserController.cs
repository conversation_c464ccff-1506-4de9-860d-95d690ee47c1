﻿using DG.Web.Framework;

using DH.Entity;

using HlktechIoT.Dto;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;

using Pek.Exceptions;
using Pek.Helpers;
using Pek.Ids;
using Pek.Models;

using System.ComponentModel;
using System.Dynamic;

using XCode;
using XCode.Membership;

using YRY.Web.Controllers.Areas.Admin;
using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Admin.Controllers;

/// <summary>
/// 用户列表
/// </summary>
[DisplayName("用户列表")]
[Description("用户列表")]
[AdminArea]
[DHMenu(100, ParentMenuName = "DHUser", ParentMenuDisplayName = "用户管理", ParentMenuUrl = "", ParentMenuOrder = 20, ParentIcon = "layui-icon-user", CurrentMenuUrl = "~/{area}/User", CurrentMenuName = "UserList", LastUpdate = "20240124")]
public class UserController : BaseAdminControllerX {
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 100;

    /// <summary>
    /// 用户列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("用户列表")]
    public IActionResult Index(Int32 CompanyId)
    {
        ViewBag.CompanyId = CompanyId;
        return View();
    }

    /// <summary>
    /// 根据条件搜索
    /// </summary>
    /// <param name="name">登录名/昵称</param>
    /// <param name="page"></param>
    /// <param name="limit"></param>
    /// <param name="did">部门ID</param>
    /// <param name="CompanyId">合作公司Id</param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("账户列表查询")]
    public IActionResult GetPageUser(String name, Int32 did,Int32 CompanyId, Int32 page = 1, Int32 limit = 10)
    {
        if (name != null) name = name.Trim();
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = UserE._.ID,
            Desc = true,
        };

        var exp = new WhereExpression();
        exp &= UserE._.Ex1 == 0;
        if (did > 0)
        {
            exp &= UserDetail._.DepartmentIds.Contains($",{did},");
        }
        if (CompanyId > 0)
        {
            var uId = UserDetail.FindAll(UserDetail._.CompanyId == CompanyId).Select(e => e.Id);
            if(uId.Count() == 0)
            {
                return Json(new { code = 0, msg = "success", count = 0, data = new List<object>() });
            }
            exp &= UserE._.ID.In(uId);
        }
        if (!name.IsNullOrWhiteSpace())
        {
            exp &= UserE._.Name.Contains(name) | UserE._.DisplayName.Contains(name) | UserE._.Mobile.Contains(name) | UserE._.Code.Contains(name) | UserE._.Mail.Contains(name);
        }
        
        IEnumerable<User> list = UserE.FindAll(exp, pages);

        var data = list.Select(x => new { x.ID, x.Name, x.DisplayName, x.RoleName, x.Mail, x.Mobile, x.Enable, DepartmentName = x.Department?.Name, x.RegisterTime });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data = data });
    }

    /// <summary>
    /// 部门树型
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("部门树型")]
    public IActionResult GetDepartmentList()
    {
        var list = DepartmentE.FindAllWithCache().OrderBy(e => e.Sort).ThenBy(e => e.ID);

        var result = new DTreeDto();
        result.status.code = 200;
        result.status.message = GetResource("操作成功");

        var listDTreeItem = new List<DTreeItem>();
        foreach (var item in list.Where(e => e.ParentID == 0))
        {
            var parentDepartment = DepartmentE.FindAllByParentId(item.ID);

            var model = new DTreeItem
            {
                id = item.ID,
                title = item.Name,
                last = !parentDepartment.Any(),
                parentId = 0
            };

            if (parentDepartment.Any())
            {
                model.children = DTreeChild(list, item);
            }

            listDTreeItem.Add(model);
        }

        result.data = listDTreeItem;

        return Json(result);
    }

    private IList<DTreeItem> DTreeChild(IEnumerable<Department> list, Department department)
    {
        IList<DTreeItem> listDTreeItem = new List<DTreeItem>();

        foreach (var item in list.Where(e => e.ParentID == department.ID))
        {
            var parentDepartment = DepartmentE.FindAllByParentId(item.ID);

            var model = new DTreeItem
            {
                id = item.ID,
                title = item.Name,
                last = !parentDepartment.Any(),
                parentId = department.ID,
            };

            if (parentDepartment.Any())
            {
                model.children = DTreeChild(list, item);
            }

            listDTreeItem.Add(model);
        }
        return listDTreeItem;
    }

    /// <summary>
    /// 部门树型
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("部门树型")]
    public IActionResult GetOrganizationUnitList(Int32 Id)
    {
        var result = new DResult();

        UserDetail? user = null;
        if (Id > 0)
        {
            user = UserDetail.FindById(Id);
        }

        var list = Department.FindAllWithCache().OrderBy(e => e.Sort).ThenBy(e => e.ID).Select(e =>
        {
            int? parentId = e.ParentID == 0 ? null : e.ParentID;
            var isAssigned = false;
            var checkArr = "0";

            if (user != null && !user.DepartmentIds.IsNullOrWhiteSpace())
            {
                if (user.DepartmentIds.Contains($",{e.ID},"))
                {
                    isAssigned = true;
                    checkArr = "1";
                }
            }

            return new { displayName = e.Name, id = e.ID, parentId = parentId, isAssigned = isAssigned, checkArr = checkArr };
        });
        result.data = list;
        result.code = 200;
        result.msg = "";

        return Json(result);
    }

    /// <summary>
    /// 新增用户
    /// </summary>
    /// <returns></returns>
    [DisplayName("新增用户")]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult Add(Int32 CompanyId)
    {
        dynamic viewModel = new ExpandoObject();

        var Roles = Role.FindAllWithCache().Select(x => new Role { ID = x.ID, Name = RoleLan.FindByRIdAndLId(x.ID, WorkingLanguage.Id, true).Name }).OrderBy(e => e.CreateTime).ThenBy(e => e.ID).ToList();

        foreach (var item in Roles.ToArray())
        {
            var m = RoleEx.FindById(item.ID);
            if (m != null)
            {
                if (m.IsAdmin)
                {
                    Roles.Remove(item);
                    continue;
                }
                if (!m.Role.IsSystem)
                {
                    Roles.Remove(item);
                    continue;
                }
            }
        }

        viewModel.Roles = Roles;
        ViewBag.CompanyId = CompanyId;
        return View(viewModel);
    }

    /// <summary>
    /// 新增用户
    /// </summary>
    /// <returns></returns>
    [DisplayName("创建用户")]
    [HttpPost]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult CreateUser(string Name, string PassWord, String Email, String Mobile, Int16 UType, String RealName, Int32 Sex, Boolean Enabled, Int32[] AssignedOrganizationUnitIds, Int32[] AssignedRoleNames, Int32 CompanyId)
    {
        try
        {
            if (Name.IsNullOrWhiteSpace())
            {
                throw new DHException(GetResource("账户名不可为空"));
            }
            if (Name.Length <= 2)
            {
                throw new DHException(GetResource("账户名长度不可少于2位"));
            }
            if (PassWord.IsNullOrWhiteSpace())
            {
                throw new DHException(GetResource("密码不可为空"));
            }
            if (PassWord.Length < 6)
            {
                throw new DHException(GetResource("密码长度不可少于6位"));
            }

            if (!Email.IsNullOrWhiteSpace())
            {
                if (!ValidateHelper.IsEmail(Email))
                {
                    throw new DHException(GetResource("邮箱格式不对"));
                }

                var m = UserE.FindByMail(Email);
                if (m != null)
                    throw new DHException(GetResource("邮箱已被其他人使用"));
            }

            if (!Mobile.IsNullOrWhiteSpace())
            {
                if (!ValidateHelper.IsMobile(Mobile))
                {
                    throw new DHException(GetResource("手机号码格式不对"));
                }

                var m = UserE.FindByMobile(Mobile);
                if (m != null)
                    throw new DHException(GetResource("手机号码已被其他人使用"));
            }

            var Model = UserE.FindByName(Name);
            if (Model != null)
            {
                throw new DHException(GetResource("用户名已存在 请重新输入！"));
            }
            var UserModel = new UserE();
            UserModel.Name = Name;
            UserModel.DisplayName = RealName;
            UserModel.Password = ManageProvider.Provider?.PasswordProvider.Hash(PassWord.Length == 32 ? PassWord : PassWord.MD5());
            UserModel.Ex1 = 0;
            UserModel.RegisterTime = DateTime.Now;
            UserModel.Mobile = Mobile;
            UserModel.Mail = Email;
            UserModel.Enable = Enabled;
            UserModel.Sex = (SexKinds)Sex;

            if (AssignedRoleNames.Length > 0)
            {
                UserModel.RoleID = AssignedRoleNames[0];
            }

            if (AssignedOrganizationUnitIds != null && AssignedOrganizationUnitIds.Any())
            {
                UserModel.DepartmentID = AssignedOrganizationUnitIds[^1];
            }

            UserModel.Insert();

            var modelUserDetail = new UserDetail();
            modelUserDetail.UType = (UserKinds)UType;//用户类型
            modelUserDetail.Id = UserModel.ID;
            modelUserDetail.TrueName = RealName;
            modelUserDetail.CompanyId = CompanyId;

            if (AssignedOrganizationUnitIds != null && AssignedOrganizationUnitIds.Any())
            {
                modelUserDetail.DepartmentIds = $",{AssignedOrganizationUnitIds.Join(",")},"; ;
            }

            modelUserDetail.Insert();

            if (modelUserDetail.UType == UserKinds.Agent || modelUserDetail.UType == UserKinds.Vendor)
            {
                UserModel.Code = IdHelper.GetNextId() + IdHelper.GetSId();
                UserModel.Update();
            }
        }
        catch (DHException ex)
        {
            return Json(new DResult { success = false, msg = ex.Message });
        }
        return Json(new DResult { success = true, msg = GetResource("创建成功") });
    }

    /// <summary>
    /// 编辑用户
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    [DisplayName("编辑用户")]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult Edit(Int32 Id)
    {
        dynamic viewModel = new ExpandoObject();

        var Roles = Role.FindAllWithCache().Select(x => new Role { ID = x.ID, Name = RoleLan.FindByRIdAndLId(x.ID, WorkingLanguage.Id, true).Name }).OrderBy(e => e.CreateTime).ThenBy(e => e.ID).ToList();

        foreach (var item in Roles.ToArray())
        {
            var m = RoleEx.FindById(item.ID);
            if (m != null)
            {
                if (m.IsAdmin)
                {
                    Roles.Remove(item);
                    continue;
                }
                if (!m.Role.IsSystem)
                {
                    Roles.Remove(item);
                    continue;
                }
            }
        }

        viewModel.Roles = Roles;

        if (Id != 0)
        {
            viewModel.UserModel = UserE.FindByID(Id);
            return View(viewModel);
        }
        else
        {
            viewModel.UserModel = new UserE();
            return View(viewModel);
        }
    }

    /// <summary>
    /// 编辑用户
    /// </summary>
    /// <returns></returns>
    [DisplayName("编辑用户")]
    [HttpPost]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult EditUser(Int32 Id, string PassWord, String Email, String Mobile, String RealName, Boolean Enabled, Int16 UType, Int32 Sex, Int32[] AssignedOrganizationUnitIds, Int32[] AssignedRoleNames)
    {
        try
        {
            if (!PassWord.IsNullOrWhiteSpace() && PassWord.Length < 6)
            {
                throw new DHException(GetResource("密码长度不可少于6位"));
            }

            if (!Email.IsNullOrWhiteSpace())
            {
                if (!ValidateHelper.IsEmail(Email))
                {
                    throw new DHException(GetResource("邮箱格式不对"));
                }

                var m = UserE.FindByMail(Email);
                if (m != null && m.ID != Id)
                {
                    throw new DHException(GetResource("邮箱已被使用"));
                }
            }

            if (!Mobile.IsNullOrWhiteSpace())
            {
                if (!ValidateHelper.IsMobile(Mobile))
                {
                    throw new DHException(GetResource("手机号码格式不对"));
                }

                var m = UserE.FindByMobile(Mobile);
                if (m != null && m.ID != Id)
                {
                    throw new DHException(GetResource("手机号码已被使用"));
                }
            }

            var UserModel = UserE.FindByID(Id);
            if (!PassWord.IsNullOrWhiteSpace())
            {
                UserModel.Password = ManageProvider.Provider?.PasswordProvider.Hash(PassWord.Length == 32 ? PassWord : PassWord.MD5());
            }
            UserModel.Mobile = Mobile;
            UserModel.Mail = Email;
            UserModel.Enable = Enabled;
            UserModel.DisplayName = RealName;
            UserModel.Sex = (SexKinds)Sex;

            if (AssignedRoleNames.Length > 0)
            {
                UserModel.RoleID = AssignedRoleNames[0];
            }

            if (AssignedOrganizationUnitIds != null && AssignedOrganizationUnitIds.Any())
                UserModel.DepartmentID = AssignedOrganizationUnitIds[^1];

            UserModel.Update();

            var modelUserDetail = UserDetail.FindById(Id);
            modelUserDetail.TrueName = RealName;
            modelUserDetail.UType = (UserKinds)UType;

            if (AssignedOrganizationUnitIds != null && AssignedOrganizationUnitIds.Any())
            {
                modelUserDetail.DepartmentIds = $",{AssignedOrganizationUnitIds.Join(",")},"; ;
            }

            modelUserDetail.Update();

            if (modelUserDetail.UType == UserKinds.Agent || modelUserDetail.UType == UserKinds.Vendor)
            {
                if (UserModel.Code.IsNullOrWhiteSpace())
                {
                    UserModel.Code = IdHelper.GetNextId() + IdHelper.GetSId();
                    UserModel.Update();
                }
            }
        }
        catch (DHException ex)
        {
            return Json(new DResult { success = false, msg = ex.Message });
        }
        return Json(new DResult { success = true, msg = GetResource("编辑成功") });
    }

    /// <summary>删除用户</summary>
    /// <returns></returns>
    [DisplayName("删除用户")]
    [HttpPost]
    [EntityAuthorize(PermissionFlags.Delete)]
    public IActionResult MemberDelete(Int32 Id)
    {
        var model = UserE.FindByID(Id.ToInt());
        if (model.RoleID == 1)
        {
            return Json(new DResult() { success = false, msg = GetResource("超级管理员不允许被删除！") });
        }

        UserE.Delete(UserE._.ID == Id);
        UserDetail.Delete(UserDetail._.Id == Id);

        UserE.Meta.Cache.Clear("", true);
        UserDetail.Meta.Cache.Clear("", true);

        return Json(new DResult() { success = true, msg = GetResource("删除成功") });
    }

    /// <summary>修改状态</summary>
    /// <returns></returns>
    [DisplayName("修改状态")]
    [HttpPost]
    [EntityAuthorize(PermissionFlags.Update)]
    public IActionResult ModifyState(Int32 Id, Boolean Status)
    {
        var result = new DResult();

        var model = UserE.FindByID(Id);
        if (model == null)
        {
            result.msg = GetResource("状态调整出错");
            return Json(result);
        }

        model.Enable = Status;
        model.Update();

        result.success = true;
        result.msg = GetResource("状态调整成功");

        return Json(result);
    }

}