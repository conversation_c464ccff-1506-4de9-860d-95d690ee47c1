﻿@{
    Html.AppendTitleParts(T("编辑生产厂家订单").Text);

    // Script
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/xm-select.js");
    Html.AppendScriptParts(ResourceLocation.Footer, "~/js/dg.js");

    bool IsAudit = Model.Status > 1;
}
<style asp-location="true">
    .layui-input-block {
        margin-right: 40px;
    }

    .containers {
        padding-top: 30px;
        padding-left: 30px;
    }

    .layui-form-item.btn {
        padding-top: 10px;
        padding-left: 115px;
    }

    .label-width span {
        color: #f00;
    }

    .layui-form-label {
        width: 94px;
        white-space:nowrap;
        min-width:110px;
    }

    .upload {
        cursor: pointer;
        max-width: 120px;
        max-height: 90px;
    }
    .select{
        max-width: 420px !important;
        border: 1px solid #e6e6e6;
    }

    .mac-address {
        display: flex;
        align-items: center;

    }
    .mac-item {
        flex: 1;
    }

    .mac-label {
        margin-bottom: 10px;
    }
</style>
<script asp-location="Head">
    var closeThis = '@T("关 闭 当 前")';
    var closeOther = '@T("关 闭 其 他")';
    var closeAll = '@T("关 闭 全 部")';

    var jsMenuStyle = "@T("菜单风格")";
    var jsTopStyle = "@T("顶部风格")";
    var jsMenu = "@T("菜单")";
    var jsView = "@T("视图")";
    var jsBanner = "@T("通栏")";
    var jsThroughColor = "@T("通色")";
    var jsFooter = "@T("页脚")";
    var jsMoreSettings = "@T("更多设置")";
    var jsOpen = "@T("开")";
    var jsClose = "@T("关")";
    var jsThemeColor = "@T("主题配色")";
    var layuiNoData = '@T("无数据")';
    var layuiAsc = "@T("升序")";
    var layuiDesc = "@T("降序")";

    var layuiPrint = '@T("打印")';
    var layuiExport = '@T("导出")';
    var layuiFilterColumn = '@T("筛选列")';
    var layuiArticlePage = '@T("条/页")';
    var layuiTotal = '@T("总共")';
    var layuiBtn = '@T("确定")';
    var layuiGoPage = '@T("到第")';
    var layuiPrev = '@T("上一页")';
    var layuiNext = '@T("下一页")';
</script>
<div class="containers">
    <form class="layui-form">
        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("订单号")</label>
            <div class="layui-input-inline" style="min-width:320px">
                <input type="text" name="OrderId" placeholder="@T("请输入订单号")" autocomplete="off" class="layui-input"  value="@Model.OrderId" @(IsAudit ? "disabled":"")>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span></span>@T("所属公司")</label>
            <div class="layui-input-inline" style="width: 320px;">
                <div id="demo1" style=" width: 100%;"></div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span></span>@T("产品型号")</label>
            <div class="layui-input-inline" style="width: 320px;">
                <div id="demo2" style=" width: 100%;"></div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width">@T("数量")</label>
            <div class="layui-input-inline" style="min-width:320px">
                <input type="text" name="Quantity" placeholder="@T("请输入数量")" autocomplete="off" class="layui-input"  value="@Model.Quantity" @(IsAudit ? "disabled":"")>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span></span>@T("生成SN")</label>
            <div class="layui-input-inline" style="width: 320px;">
                <input type="checkbox" name="NeedSn" lay-filter="NeedSn" lay-skin="switch" @(ViewBag.StateInfo.NeedSn == true ? "checked" : "") disabled>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span></span>@T("生成主Mac")</label>
            <div class="layui-input-inline" style="width: 320px;">
                <input type="checkbox" name="NeedMac" lay-filter="NeedMac" lay-skin="switch" @(ViewBag.StateInfo.NeedMac == true ? "checked" : "") disabled>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span></span>@T("生成副Mac")</label>
            <div class="layui-input-inline" style="width: 320px;">
                <input type="checkbox" name="NeedMac1" lay-filter="NeedMac1" lay-skin="switch" @(ViewBag.StateInfo.NeedMac1 == true ? "checked" : "") disabled>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span></span>@T("PCB周期")</label>
            <div class="layui-input-inline" style="width: 320px;">
                <input type="checkbox" name="PCBCycle" lay-filter="PCBCycle" lay-skin="switch" @(ViewBag.StateInfo.PCBCycle == true ? "checked" : "") disabled>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span></span>@T("屏蔽罩周期")</label>
            <div class="layui-input-inline" style="width: 320px;">
                <input type="checkbox" name="ShieldCycle" lay-filter="ShieldCycle" lay-skin="switch" @(ViewBag.StateInfo.ShieldCycle == true ? "checked" : "") disabled>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label label-width"><span></span>@T("主芯片周期")</label>
            <div class="layui-input-inline" style="width: 320px;">
                <input type="checkbox" name="MainChipCycle" lay-filter="MainChipCycle" lay-skin="switch" @(ViewBag.StateInfo.MainChipCycle == true ? "checked" : "") disabled>
            </div>
        </div>

        <div class="mac-range" id="macRange">
            <div class="layui-form-item mac-range-item" id="macRangeItem">
                <label class="layui-form-label label-width">MAC地址范围</label>
                <div class="mac-address">
                    <div class="mac-item">
                        <input type="text" name="startMac" class="layui-input"  placeholder="请输入MAC地址" @(IsAudit ? "disabled":"")>
                    </div>
                    <div style="margin: 0 10px;">
                        -
                    </div>
                    <div class="mac-item">
                        <input type="text" name="endMac" class="layui-input"  placeholder="请输入MAC地址" @(IsAudit ? "disabled":"")>
                    </div>
                    <input type="button" class="pear-btn pear-btn-primary pear-btn-normal add-mac" style="margin-left: 10px; font-size: 20px;" value="+" @(IsAudit ? "disabled":"")>
                </div>
            </div>
        </div>
        <div class="layui-form-item btn">
            <input hidden name="Id" value="@Model.Id" /> 
            <input type="button" lay-submit lay-filter="Submit" id="Submit" class="pear-btn pear-btn-primary pear-btn-normal" style="margin-left:20px" value="@T("保存")">
        </div>
    </form>
</div>

<script asp-location="Footer">
    layui.use(['abp', 'form', 'dgcommon', 'dg', 'upload'], function () {
        var $ = layui.$;
        var abp = layui.abp;
        var form = layui.form;
        var os = layui.dgcommon;
        var dg = layui.dg;
        var laydate = layui.laydate;

        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.iframeAuto(index);
        //时间插件  
        var startDate = laydate.render({
            elem: '#start',
            btns: ['clear', "confirm"],//只显示清空和确定按钮
            type: 'datetime',       // 设置日期选择类型为年月
            format: 'yyyy-MM-dd HH:mm:ss',   // 设置日期的格式，这里是年-月
            value:'@Model.StartTime',
            choose: function (date) {
                // 用户选择日期的回调函数
                // 在这里可以处理用户选择日期后的逻辑
                laydate.close(); // 关闭日期选择器弹窗
            }
        });

        var endDate = laydate.render({
            elem: '#end',
            btns: ["clear", "confirm"],
            type: 'datetime',       // 设置日期选择类型为年月
            format: 'yyyy-MM-dd HH:mm:ss',   // 设置日期的格式，这里是年-月
            value:'@Model.EndTime',
            choose: function (date) {
                console.log(date);
                // 用户选择日期的回调函数
                // 在这里可以处理用户选择日期后的逻辑
                laydate.close(); // 关闭日期选择器弹窗
            }
        });
        
                // 监听主Mac开关
        form.on('switch(NeedMac)', function (data) {
            if (!data.elem.checked) {
                // 主Mac未启用，自动关闭并禁用副Mac
                $("input[name='NeedMac1']").prop("checked", false);
                $("input[name='NeedMac1']").prop("disabled", true);
                form.render('checkbox');
            } else {
                // 主Mac启用，允许副Mac可选
                $("input[name='NeedMac1']").prop("disabled", false);
                form.render('checkbox');
            }
        });

        // 页面初始化时判断一次
        $(function () {
            if (!$("input[name='NeedMac']").prop("checked")) {
                $("input[name='NeedMac1']").prop("checked", false);
                $("input[name='NeedMac1']").prop("disabled", true);
                layui.form.render('checkbox');
            }
        });

        $("#macRange").hide();

        var isNeedMacranges = false
        let macranges = []
        const jsonArr = '@Model?.MacRange'
        if (jsonArr && jsonArr.startsWith('[') && jsonArr.endsWith(']')) {
            macranges = JSON.parse(htmlDecodeByRegExp('@Model?.MacRange'))
        }
        

        macranges.forEach((item, index) => {
            isNeedMacranges = true
            let html = `<div class="layui-form-item mac-range-item">
                <label class="layui-form-label label-width">${index === 0 ? 'MAC地址范围' : ''}</label>
                <div class="mac-address">
                    <div class="mac-item">
                        <input type="text" name="startMac${index === 0 ? '' : index}" class="layui-input" value="${item.Start}" placeholder="请输入MAC地址" @(IsAudit ? "disabled":"")>
                    </div>
                    <div style="margin: 0 10px;">
                        -
                    </div>
                    <div class="mac-item">
                        <input type="text" name="endMac${index === 0 ? '' : index}" class="layui-input" value="${item.End}" placeholder="请输入MAC地址" @(IsAudit ? "disabled":"")>
                    </div>
                    <input type="button" class="pear-btn pear-btn-primary pear-btn-normal add-mac" style="margin-left: 10px; font-size: 20px;" value="+" @(IsAudit ? "disabled":"")>
                </div>
            </div>`
            $('#macRange').append(html)
        })
        $('#macRange').on('click', '.add-mac', function(e) {
            let inputCount = 0
            if (macranges.length > 0) {
                inputCount = $('#macRange .mac-range-item').length - 1;
            } else {
                inputCount = $('#macRange .mac-range-item').length;
            }
            let html = `<div class="layui-form-item mac-range-item">
                <label class="layui-form-label label-width"></label>
                <div class="mac-address">
                    <div class="mac-item">
                        <input type="text" name="startMac${inputCount}" class="layui-input" placeholder="请输入MAC地址" />
                    </div>
                    <div style="margin: 0 10px;">
                        -
                    </div>
                    <div class="mac-item">
                        <input type="text" name="endMac${inputCount}" class="layui-input" placeholder="请输入MAC地址" />
                    </div>
                    <input type="button" class="pear-btn pear-btn-primary pear-btn-normal add-mac" style="margin-left: 10px; font-size: 20px;" value="+" />
                </div>
            </div>`
            $('#macRange').append(html)
        })

        var demo1 = xmSelect.render({
            el: '#demo1',
            radio: true, //设置单选
            name: 'CompanyId',
            paging: true,  // 是否翻页
            pageSize: 10,  // 每页数量
            filterable: true,  // 搜索模式
            remoteSearch: true,  // 远程搜索
            clickClose: true,
            pageRemote: true,  // 分页
            disabled: @IsAudit.ToString().ToLower(), // 设置禁用
            remoteMethod: function (val, cb, show, pageIndex) {  // 远程方法
                var obj = [];

                // 接口数据
                $.post('@Url.Action("SearchCompany")', { keyword: val, page: pageIndex,Id:'@Model.CompanyId' }, function (res) {
                    // console.log('搜索后的数据',res);
                    if (res.success) {
                        if (res.data != null) {
                            if (res.extdata.data != null) {
                                demo1.setValue(res.extdata.data)// 传入一个-默认值-数组
                            }
                            cb(res.data, res.extdata.PageCount);
                        }
                    }
                    else {
                        cb(obj, 0);
                        os.error(res.data.msg);
                    }
                });

            },
            on: function (data) {  // 监听选择
                if (data.arr.length > 0) {

                }
            }
        });

        $('#macRangeItem').hide()

        
        var isFirstDemo2 = true; // 标志变量
        var demo2 = xmSelect.render({
            el: '#demo2',
            radio: true, //设置单选
            name: 'ProductTypeId',
            paging: true,  // 是否翻页
            pageSize: 10,  // 每页数量
            filterable: true,  // 搜索模式
            remoteSearch: true,  // 远程搜索
            clickClose: true,
            pageRemote: true,  // 分页
            disabled: @IsAudit.ToString().ToLower(), // 设置禁用
            remoteMethod: function (val, cb, show, pageIndex) {  // 远程方法
                var obj = [];

                // 接口数据
                $.post('@Url.Action("SearchProductType")', { keyword: val, page: pageIndex,Id:'@Model.ProductTypeId' }, function (res) {
                    // console.log('搜索后的数据',res);
                    if (res.success) {
                        if (res.data != null) {
                            if (res.extdata.data != null) {
                                demo2.setValue(res.extdata.data)// 传入一个-默认值-数组
                            }
                            cb(res.data, res.extdata.PageCount);
                        }
                    }
                    else {
                        cb(obj, 0);
                        os.error(res.data.msg);
                    }
                });

            },
            on: function (data) {  // 监听选择

                    if (data.arr[0].NeedMac || data.arr[0].NeedMac1) {
                        $("#macRange").show();
                        macranges.length === 0 && $('#macRangeItem').show()
                        isNeedMacranges = true
                    }else {
                        $("#macRange").hide();
                        macranges.length === 0 && $('#macRangeItem').hide()
                        isNeedMacranges = false
                    }

                if (isFirstDemo2) {
                    isFirstDemo2 = false;
                    return; // 第一次不执行
                }

                console.log(data);

                    if(data.arr[0].NeedSn){
                        $("input[name='NeedSn']").prop("checked", true);
                    }else{
                        $("input[name='NeedSn']").prop("checked", false);
                    }
                    if(data.arr[0].NeedMac){
                        $("input[name='NeedMac']").prop("checked", true);
                    }else{
                        $("input[name='NeedMac']").prop("checked", false);
                    }

                    if(data.arr[0].NeedMac1){
                        $("input[name='NeedMac1']").prop("checked", true);
                    }else{
                        $("input[name='NeedMac1']").prop("checked", false);
                    }
                    if(data.arr[0].PCBCycle){
                        $("input[name='PCBCycle']").prop("checked", true);
                    }else{
                        $("input[name='PCBCycle']").prop("checked", false);
                    }
                    if(data.arr[0].ShieldCycle){
                        $("input[name='ShieldCycle']").prop("checked", true);
                    }else{
                        $("input[name='ShieldCycle']").prop("checked", false);
                    }
                    if(data.arr[0].MainChipCycle){
                        $("input[name='MainChipCycle']").prop("checked", true);
                    }else{
                        $("input[name='MainChipCycle']").prop("checked", false);
                    }
                    layui.form.render('checkbox');
            }
        });
        form.on('submit(Submit)', function (data) {

            if('@Model.Status' > 1){
                abp.notify.error("禁止编辑");
                return;
            }

            // console.log(data);
            if (isNeedMacranges) {
                let inputCount = $('#macRange .mac-range-item').length - 1;
                if (macranges.length === 0) {
                    inputCount = $('#macRange .mac-range-item').length;
                }
                console.log('inputCOunt => ',inputCount)
                const field = data.field
                let Macranges = []
                for(let i = 0; i < inputCount; i++) {
                    const start = i === 0 ? field.startMac : field[`startMac${i}`]
                    const end = i === 0 ? field.endMac : field[`endMac${i}`]
                    if (!((start && end) || (!start && !end))) return layer.msg('请填写起始和结束MAC地址')
                    Macranges.push({
                        start,
                        end
                    })
                }
                field.Macranges = Macranges
            }
            var waitIndex = parent.layer.load(2);
            abp.ajax({
                url: "@Url.Action("Edit")",
                contentType : "application/x-www-form-urlencoded; charset=utf-8",
                data: data.field,
                abpHandleError: false
            }).done(function (data) {
                if (!data.success) {
                    abp.notify.error(data.msg);
                    return false;
                }
                parent.layer.closeAll();
                dg.reload('tables');
                parent.layui.abp.notify.success(data.msg);
            }).fail(function (jqXHR) {
                parent.layer.msg(jqXHR.message, { icon: 5 });
            }).always(function () {
                parent.layer.close(waitIndex);
            });

            return false;
        });

    });
</Script>