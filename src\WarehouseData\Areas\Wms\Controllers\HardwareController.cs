﻿using DG.Web.Framework;

using DH.Entity;

using HlktechIoT.Dto;
using HlktechIoT.Dto.Export;
using HlktechIoT.Entity;

using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;

using Microsoft.AspNetCore.Mvc;

using NewLife;
using NewLife.Data;

using Pek;
using Pek.Helpers;
using Pek.Models;

using System.ComponentModel;

using XCode.Membership;

using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Wms.Controllers;

/// <summary>设备管理</summary>
[DisplayName("设备管理")]
[Description("仓库里常规设备管理")]
[WmsArea]
[DHMenu(90,ParentMenuName = "HardwareDevice", ParentMenuDisplayName = "硬件管理", ParentMenuUrl = "", ParentMenuOrder = 40, ParentIcon = "layui-icon-component", CurrentMenuUrl = "~/{area}/Hardware", CurrentMenuName = "HardwareList", LastUpdate = "20240418")]
public class HardwareController : BaseAdminControllerX {
    /// <summary>菜单顺序。扫描是会反射读取</summary>
    protected static Int32 MenuOrder { get; set; } = 90;

    /// <summary>
    /// 设备列表
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("设备列表")]
    public IActionResult Index()
    {
        return View();
    }

    /// <summary>
    /// 根据条件搜索
    /// </summary>
    /// <param name="key">设备Mac地址/其他标志/设备型号/绑定用户姓名等</param>
    /// <param name="page"></param>
    /// <param name="limit"></param>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Detail)]
    [DisplayName("设备列表")]
    public IActionResult GetList(String key, Int32 page = 1, Int32 limit = 10)
    {
        if (!key.IsNullOrWhiteSpace()) key = key.SafeString().Trim();
        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = HardwareDevices._.Id,
            Desc = true,
        };

        var list = HardwareDevices.Search(key, pages);

        var data = list.Select(x => new { x.Id, x.Mac, x.Code, HType = GetResource(x.HType.ToString()), x.DeviceModel, x.Remark, x.UpdateUser, x.UpdateTime, x.BindUserRealName, Status = (Int32)x.Status == 0 ? "" : GetResource(x.Status.ToString()) });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>
    /// 增加设备
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("增加设备")]
    public IActionResult Add()
    {
        return View();
    }

    /// <summary>
    /// 增加设备
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Insert)]
    [DisplayName("增加设备")]
    [HttpPost]
    public IActionResult Add(String Mac, String Code, Int16 HType, String DeviceModel, String Remark, Int32 UId, Int32 Status)
    {
        var result = new DResult();

        if (Mac.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("设备Mac地址不能为空");
            return Json(result);
        }

        if (DeviceModel.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("设备型号不能为空");
            return Json(result);
        }

        if (HardwareDevices.FindByMac(Mac) != null)
        {
            result.msg = GetResource("设备Mac地址和设备类型不能重复");
            return Json(result);
        }

        var model = new HardwareDevices();
        model.Mac = Mac;
        model.Code = Code;
        model.HType = (HardwareType)HType;
        model.DeviceModel = DeviceModel;
        model.Remark = Remark;
        model.BindUserID = UId;
        model.BindUser = model.User?.Name;
        model.BindUserRealName = model.UserDetail?.TrueName;
        model.Status = (HardwareStatus)Status;
        model.Insert();

        var modelDeviceLogs = new DeviceLogs();
        modelDeviceLogs.DId = model.Id;
        modelDeviceLogs.Process = Status;
        modelDeviceLogs.SaveAsync();

        result.success = true;
        result.msg = GetResource("成功新增设备");

        return Json(result);
    }

    /// <summary>
    /// 编辑设备
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("编辑设备")]
    public IActionResult Edit(Int32 Id)
    {
        var model = HardwareDevices.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("设备不存在"));
        }

        return View(model);
    }

    /// <summary>
    /// 编辑设备
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Update)]
    [DisplayName("编辑设备")]
    [HttpPost]
    public IActionResult Edit(Int32 Id, String Mac, String Code, Int16 HType, String DeviceModel, String Remark, Int32 UId, Int32 Status)
    {
        var result = new DResult();

        if (Mac.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("设备Mac地址不能为空");
            return Json(result);
        }

        if (DeviceModel.IsNullOrWhiteSpace())
        {
            result.msg = GetResource("设备型号不能为空");
            return Json(result);
        }

        var model = HardwareDevices.FindByMac(Mac);
        if (model != null && model.Id != Id)
        {
            result.msg = GetResource("设备Mac地址和设备类型不能重复");
            return Json(result);
        }

        model = HardwareDevices.FindById(Id);
        if (model == null)
        {
            result.msg = GetResource("设备不存在");
            return Json(result);
        }

        model.Mac = Mac;
        model.Code = Code;
        model.HType = (HardwareType)HType;
        model.DeviceModel = DeviceModel;
        model.Remark = Remark;
        model.BindUserID = UId;
        model.BindUser = model.User?.Name;
        model.BindUserRealName = model.UserDetail?.TrueName;
        model.Status = (HardwareStatus)Status;
        model.Update();

        var modelDeviceLogs = new DeviceLogs();
        modelDeviceLogs.DId = model.Id;
        modelDeviceLogs.Process = Status;
        modelDeviceLogs.SaveAsync();

        result.success = true;
        result.msg = GetResource("成功编辑设备");

        return Json(result);
    }

    /// <summary>
    /// 删除设备
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize(PermissionFlags.Delete)]
    [DisplayName("删除设备")]
    [HttpPost]
    public IActionResult Delete(Int32 Id)
    {
        var res = new DResult();

        HardwareDevices.Delete(HardwareDevices._.Id == Id);

        res.success = true;
        res.msg = GetResource("删除成功");
        return Json(res);
    }

    /// <summary>搜索用户</summary>
    /// <returns></returns>
    [DisplayName("搜索用户")]
    [EntityAuthorize(PermissionFlags.Insert)]
    public IActionResult SearchUser(String keyword, Int32 Id, Int32 page,String key)
    {
        var res = new DResult();

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = 10,
            RetrieveTotalCount = true,
            Sort = UserE._.ID,
            Desc = true,
        };

        res.data = UserE.Search(keyword, pages).WhereIf(key.IsNotNullAndWhiteSpace(),e=>e.DisplayName.Contains(key)).Select(e =>
        {
            return new Xmselect<Int32>
            {
                name = e.DisplayName,
                value = e.ID
            };
        });

        res.success = true;

        var model = HardwareDevices.FindById(Id);
        if (model != null && model.User != null)
        {
            res.extdata = new { pages.PageCount, data = new List<NameValueL<Int32?>>() { new() { name = model.User.DisplayName, value = model.User.ID } } };
        }
        else
        {
            res.extdata = new { pages.PageCount };
        }

        return Json(res);
    }

    /// <summary>
    /// 查看日志
    /// </summary>
    /// <returns></returns>
    [EntityAuthorize((PermissionFlags)16)]
    [DisplayName("查看日志")]
    public IActionResult Logs(Int32 Id)
    {
        if (Id <= 0)
        {
            return Content(GetResource("设备编号不能为空"));
        }

        var model = HardwareDevices.FindById(Id);
        if (model == null)
        {
            return Content(GetResource("设备不存在"));
        }

        return View(model);
    }

    /// <summary>
    /// 根据条件搜索
    /// </summary>
    /// <param name="Id">设备Id</param>
    /// <param name="page"></param>
    /// <param name="limit"></param>
    /// <param name="process">操作工序。1为打单，2为领料，3为生产，4为生产审核，5为打包</param>
    /// <param name="createUserId">创建者</param>
    /// <param name="start">创建时间开始</param>
    /// <param name="end">创建时间结束</param>
    /// <returns></returns>
    [EntityAuthorize((PermissionFlags)16)]
    [DisplayName("查看日志")]
    public IActionResult GetLogList(Int32 Id, Int32 process, Int32 createUserId, DateTime start, DateTime end, Int32 page = 1, Int32 limit = 10)
    {
        var model = HardwareDevices.FindById(Id);
        if (model == null) return Json(new { code = 0, msg = "success", count = 0 });

        var pages = new PageParameter
        {
            PageIndex = page,
            PageSize = limit,
            RetrieveTotalCount = true,
            Sort = DeviceLogs._.Id,
            Desc = true,
        };

        var list = DeviceLogs.Search(Id, process, createUserId, start, end, pages);

        var data = list.Select(x => new { x.Id, x.DId, x.HardwareDevices?.Mac, x.HardwareDevices?.Code, Process = (x.Process == 0 ? "" : GetResource(((Dto.HardwareStatus)x.Process).ToString())), x.CreateUser, x.CreateTime });

        return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
    }

    /// <summary>导出全部</summary>
    /// <returns></returns>
    [DisplayName("导出")]
    [EntityAuthorize((PermissionFlags)32)]
    public async Task<IActionResult> ExportAll(String key)
    {
        IExporter exporter = new ExcelExporter();

        var list = HardwareDevices.Search(key, null).Select(e => new DeviceExport()
        {
            Mac = e.Mac,
            Code = e.Code,
            HType = GetResource(e.HType.ToString()),
            DeviceModel = e.DeviceModel,
            BindUserRealName = e.BindUserRealName,
            Status = (Int32)e.Status == 0 ? "" : GetResource(e.Status.ToString()),
            Remark = e.Remark,
        });

        var result = await exporter.ExportAsByteArray(list.ToList());

        return File(result, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"DeviceExport{DateTime.Now:yyyyMMddhhmm}.xlsx");
    }
}
