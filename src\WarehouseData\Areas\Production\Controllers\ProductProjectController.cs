﻿using DG.Web.Framework;
using HlktechIoT.Entity;
using Microsoft.AspNetCore.Mvc;
using NewLife;
using NewLife.Data;
using Pek;
using Pek.Models;
using System.ComponentModel;
using XCode.Membership;
using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Production.Controllers
{
    /// <summary>产品项目</summary>
    [DisplayName("产品项目")]
    [Description("产品项目")]
    [ProductionArea]
    [DHMenu(95, ParentMenuName = "ProductionsManager", ParentMenuDisplayName = "生产管理", ParentMenuUrl = "", ParentMenuOrder = 30, ParentIcon = "layui-icon-component", CurrentMenuUrl = "~/{area}/ProductProject", CurrentMenuName = "ProductProjectList", LastUpdate = "20250616")]
    public class ProductProjectController : BaseAdminControllerX
    {
        protected static Int32 MenuOrder { get; set; } = 95;

        /// <summary>
        /// 产品项目列表
        /// </summary>
        /// <returns></returns>
        [DisplayName("列表")]
        [EntityAuthorize(PermissionFlags.Detail)]
        public IActionResult Index()
        {
            return View();
        }

        /// <summary>
        /// 产品项目列表
        /// </summary>
        /// <param name="FirmwaresId">生产固件id</param>
        /// <param name="Key">关键字</param>
        /// <param name="page">页码</param>
        /// <param name="limit">条数</param>
        /// <returns></returns>
        [DisplayName("列表")]
        [EntityAuthorize(PermissionFlags.Detail)]
        public IActionResult GetList(Int64 FirmwaresId, String Key, Int32 page, Int32 limit)
        {
            var pages = new PageParameter
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "Id",
                Desc = true,
            };
            var data = ProductProject.Search(FirmwaresId, null, DateTime.MinValue, DateTime.MinValue, Key, pages).Select(e => new
            {
                e.Id,
                e.Name,
                ProductTypeName = e.ProductType?.Name,
                e.Material,
                e.Status,
                e.CreateTime,
            });
            return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
        }

        /// <summary>
        /// 搜索产品型号
        /// </summary>
        /// <param name="keyword">关键字</param>
        /// <param name="page">页码</param>
        /// <param name="Id">产品型号id</param>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("搜索产品型号")]
        [EntityAuthorize(PermissionFlags.Detail)]
        public IActionResult SearchProductType(String keyword, Int32 page, Int32 Id)
        {
            var res = new DResult();

            var pages = new PageParameter
            {
                PageIndex = page,
                PageSize = 10,
                RetrieveTotalCount = true,
                Sort = ProductType._.Id,
                Desc = true,
            };

            res.data = ProductType.Search(0, keyword, true, DateTime.MinValue, DateTime.MinValue, "", pages).Select(e =>
            {
                return new
                {
                    name = e.Name,
                    value = e.Id,
                    e.NeedSn,
                    e.NeedMac,
                    e.NeedMac1,
                    e.PCBCycle,
                    e.ShieldCycle,
                    e.MainChipCycle,
                };
            });

            res.success = true;

            var model = ProductType.FindById(Id);

            if (model == null)
            {
                res.extdata = new { pages.PageCount };
            }
            else
            {
                res.extdata = new { pages.PageCount, data = new List<object>() { new { name = model.Name, value = model.Id, NeedSn = model.NeedSn, NeedMac = model.NeedMac, model.NeedMac1, model.PCBCycle, model.ShieldCycle, model.MainChipCycle } } };
            }

            return Json(res);
        }

        /// <summary>
        /// 添加产品项目
        /// </summary>
        /// <returns></returns>
        [DisplayName("添加")]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult Add()
        {
            return View();
        }

        /// <summary>
        /// 添加产品项目
        /// </summary>
        /// <param name="Name">名称</param>
        /// <param name="Material">物料编号</param>
        /// <param name="Status">启用状态</param>
        /// <param name="Remark">备注</param>
        /// <param name="DataCategoryIds">必须上传的资料分类</param>
        /// <param name="ProductTypeId">产品型号id</param>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("添加")]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult Add([FromForm] String Name, [FromForm] String Material, [FromForm] Boolean Status, [FromForm] String Remark, [FromForm] String DataCategoryIds,[FromForm]Int32 ProductTypeId)
        {
            DResult res = new();
            if (Name.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("名称不能为空");
                return Json(res);
            }
            if (ProductTypeId <= 0)
            {
                res.msg = GetResource("产品型号不能为空");
                return Json(res);
            }
            var modelProductType = ProductType.FindById(ProductTypeId);
            if (modelProductType == null)
            {
                res.msg = GetResource("产品型号不存在");
                return Json(res);
            }
            var modelProductProject = ProductProject.FindByName(Name);
            if(modelProductProject != null)
            {
                res.msg = GetResource("名称已存在");
                return Json(res);
            }
            var model = new ProductProject()
            {
                Name = Name,
                Material = Material,
                ProductTypeId = ProductTypeId,
                Status = Status,
                Remark = Remark,
            };
            if (!DataCategoryIds.IsNullOrWhiteSpace())
            {
                model.DataCategoryIds = "," + DataCategoryIds + ",";
            }
            model.Insert();
            res.success = true;
            res.msg = GetResource("添加成功");
            return Json(res);
        }

        /// <summary>
        /// 编辑产品项目
        /// </summary>
        /// <param name="Id">项目id</param>
        /// <returns></returns>
        [DisplayName("编辑")]
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult Update(Int32 Id)
        {
            var model = ProductProject.FindById(Id);
            if (model == null)
            {
                return Content(GetResource("项目信息不存在"));
            }
            return View(model);
        }

        /// <summary>
        /// 编辑产品项目
        /// </summary>
        /// <param name="Name">名称</param>
        /// <param name="Material">物料编号</param>
        /// <param name="Status">启用状态</param>
        /// <param name="Remark">备注</param>
        /// <param name="DataCategoryIds">必须上传的资料分类</param>
        /// <param name="Id">项目id</param>
        /// <param name="ProductTypeId">产品类型id</param>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("编辑")]
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult Update([FromForm] String Name, [FromForm] String Material, [FromForm] Boolean Status, [FromForm] String Remark, [FromForm] String DataCategoryIds, [FromForm]Int32 Id, [FromForm] Int32 ProductTypeId)
        {
            DResult res = new();
            if (Name.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("名称不能为空");
                return Json(res);
            }
            if (ProductTypeId <= 0)
            {
                res.msg = GetResource("产品型号不能为空");
                return Json(res);
            }
            var modelProductType = ProductType.FindById(ProductTypeId);
            if (modelProductType == null)
            {
                res.msg = GetResource("产品型号不存在");
                return Json(res);
            }
            var model = ProductProject.FindById(Id);
            if (model == null)
            {
                res.msg = GetResource("项目信息不存在");
                return Json(res);
            }
            var modelProductProject = ProductProject.FindByName(Name);
            if (modelProductProject != null && modelProductProject.Id != Id)
            {
                res.msg = GetResource("名称已存在");
                return Json(res);
            }
            model.Name = Name;
            model.Material = Material;
            model.ProductTypeId = ProductTypeId;
            model.Status = Status;
            model.Remark = Remark;
            if (!DataCategoryIds.IsNullOrWhiteSpace())
            {
                model.DataCategoryIds = "," + DataCategoryIds + ",";
            }
            else
            {
                model.DataCategoryIds = null;
            }
            model.Update();
            res.success = true;
            res.msg = GetResource("编辑成功");
            return Json(res);
        }

        /// <summary>
        /// 修改产品项目启用状态
        /// </summary>
        /// <param name="Id">项目id</param>
        /// <param name="Status">启用状态</param>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("修改状态")]
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult ModifyState(Int32 Id, Boolean Status)
        {
            var result = new DResult();

            var model = ProductProject.FindById(Id);
            if (model == null)
            {
                result.msg = GetResource("状态调整出错");
                return Json(result);
            }

            model.Status = Status;
            model.Update();

            result.success = true;
            result.msg = GetResource("状态调整成功");

            return Json(result);
        }

        /// <summary>
        /// 删除产品项目
        /// </summary>
        /// <param name="Id">项目id</param>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("删除")]
        [EntityAuthorize(PermissionFlags.Delete)]
        public IActionResult Delete(Int32 Id)
        {
            var res = new DResult();
            ProductProject.Delete(ProductProject._.Id == Id);
            res.success = true;
            res.msg = GetResource("删除成功");
            return Json(res);

        }
    }
}


