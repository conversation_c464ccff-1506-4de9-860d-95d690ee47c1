﻿using DG.Web.Framework;
using DH;
using DH.Entity;
using HlktechIoT.Entity;
using Microsoft.AspNetCore.Mvc;
using NewLife;
using NewLife.Data;
using NewLife.Log;
using Pek;
using Pek.Helpers;
using Pek.Models;
using Pek.Webs;
using System.ComponentModel;
using XCode;
using XCode.Membership;
using YRY.Web.Controllers.Common;

namespace HlktechIoT.Areas.Production.Controllers
{
    /// <summary>产品资料</summary>
    [DisplayName("产品资料")]
    [Description("产品资料")]
    [ProductionArea]
    [DHMenu(82, ParentMenuName = "ProductionsManager", ParentMenuDisplayName = "生产管理", ParentMenuUrl = "", ParentMenuOrder = 30, ParentIcon = "layui-icon-component", CurrentMenuUrl = "~/{area}/ProductData", CurrentMenuName = "ProductDataList", LastUpdate = "20250616")]
    public class ProductDataController : BaseAdminControllerX
    {
        protected static Int32 MenuOrder { get; set; } = 82;

        /// <summary>
        /// 产品资料列表
        /// </summary>
        /// <returns></returns>
        [DisplayName("列表")]
        [EntityAuthorize(PermissionFlags.Detail)]
        public IActionResult Index()
        {
            return View();
        }

        /// <summary>
        /// 产品资料列表
        /// </summary>
        /// <param name="page">页码</param>
        /// <param name="limit">条数</param>
        /// <param name="start">开始时间</param>
        /// <param name="end">结束时间</param>
        /// <param name="ProductDataCategoryId2">资料子分类</param>
        /// <param name="ProductProjectId">产品项目id</param>
        /// <returns></returns>
        [DisplayName("列表")]
        [EntityAuthorize(PermissionFlags.Detail)]
        public IActionResult GetList(Int32 page, Int32 limit, DateTime start, DateTime end, Int32 ProductDataCategoryId2, Int32 ProductProjectId)
        {
            var pages = new PageParameter
            {
                PageIndex = page,
                PageSize = limit,
                RetrieveTotalCount = true,
                Sort = "Id",
                Desc = true,
            };

            Int32 productDataCategoryId1 = -1;
            Int32 UId = -1;

            var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
            if (!modelRole.IsAdmin)
            {
                UId = ManageProvider.User?.ID ?? 0;
                var roleId1 = Role.GetOrAdd("硬件工程师").ID;
                var roleId2 = Role.GetOrAdd("软件工程师").ID;
                if(modelRole.Id == roleId1)
                {
                    productDataCategoryId1 = ProductDataCategory.FindByName("硬件")?.Id ?? 0;
                }
                else if (modelRole.Id == roleId2)
                {
                    productDataCategoryId1 = ProductDataCategory.FindByName("软件")?.Id ?? 0;
                }
                else
                {
                    productDataCategoryId1 = 0;
                } 
            }

            var data = ProductData.Search(UId,ProductProjectId, productDataCategoryId1, ProductDataCategoryId2, start, end, "", pages).Select(e => new
            {
                Id = e.Id.SafeString(),
                e.FilePath,
                e.Remark,
                e.ProductTypeName,
                e.ProductProjectName,
                ProductDataCategoryName1 = ProductDataCategory.FindById(e.ProductDataCategoryId1)?.Name,
                ProductDataCategoryName2 = ProductDataCategory.FindById(e.ProductDataCategoryId2)?.Name,
                e.CreateTime,
                e.CreateUser,
            });

            return Json(new { code = 0, msg = "success", count = pages.TotalCount, data });
        }

        /// <summary>
        /// 搜索产品项目
        /// </summary>
        /// <param name="keyword">关键字</param>
        /// <param name="page">页码</param>
        /// <param name="Id">项目id</param>
        /// <returns></returns>
        [DisplayName("搜索产品项目")]
        [EntityAuthorize(PermissionFlags.Detail)]
        public IActionResult SearchProductProject(String keyword, Int32 page, Int32 Id)
        {
            var res = new DResult();
            var pages = new PageParameter
            {
                PageIndex = page,
                PageSize = 10,
                RetrieveTotalCount = true,
                Sort = ProductProject._.Id,
                Desc = true,
            };

            res.data = ProductProject.Search(0, true, DateTime.MinValue, DateTime.MinValue, keyword, pages).Select(e =>
            {
                return new
                {
                    name = e.Name,
                    value = e.Id,
                };
            });

            var model = ProductProject.FindById(Id);
            if (model != null)
            {
                res.extdata = new { pages.PageCount, data = new List<object>() { new { name = model.Name, value = model.Id } } };
            }
            else
            {
                res.extdata = new { pages.PageCount };
            }

            res.success = true;

            return Json(res);
        }

        /// <summary>
        /// 搜索产品资料分类
        /// </summary>
        /// <param name="keyword">关键字</param>
        /// <param name="page">页码</param>
        /// <param name="Id">分类id</param>
        /// <returns></returns>
        [DisplayName("搜索资料类别")]
        [EntityAuthorize(PermissionFlags.Detail)]
        public IActionResult SearchDataCategory(String keyword, Int32 page, Int32 Id)
        {
            var res = new DResult();
            var pages = new PageParameter
            {
                PageIndex = page,
                PageSize = 10,
                RetrieveTotalCount = true,
                Sort = ProductDataCategory._.Id,
            };

            Int32 productDataCategoryId = -1;

            var modelRole = RoleEx.FindById(ManageProvider.User?.RoleID);
            if (!modelRole.IsAdmin)
            {
                var roleId1 = Role.GetOrAdd("硬件工程师").ID;
                var roleId2 = Role.GetOrAdd("软件工程师").ID;
                if (modelRole.Id == roleId1)
                {
                    productDataCategoryId = ProductDataCategory.FindByName("硬件")?.Id ?? 0;
                }
                else if (modelRole.Id == roleId2)
                {
                    productDataCategoryId = ProductDataCategory.FindByName("软件")?.Id ?? 0;
                }
                else
                {
                    productDataCategoryId = 0;
                }
            }

            var exp = new WhereExpression();
            exp &= ProductDataCategory._.ParentId > 0 & ProductDataCategory._.Status == true;
            if(productDataCategoryId >= 0) exp &= ProductDataCategory._.ParentId == productDataCategoryId;

            res.data = ProductDataCategory.FindAll(exp, pages).Select(e =>
            {
                return new
                {
                    name = e.Name,
                    value = e.Id,
                };
            });

            var model = ProductDataCategory.FindById(Id);
            if (model != null)
            {
                res.extdata = new { pages.PageCount, data = new List<object>() { new { name = model.Name, value = model.Id } } };
            }
            else
            {
                res.extdata = new { pages.PageCount };
            }

            res.success = true;

            return Json(res);
        }

        /// <summary>
        /// 添加产品资料
        /// </summary>
        /// <returns></returns>
        [DisplayName("添加")]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult Add()
        {
            return View();
        }

        /// <summary>
        /// 上传资料文件
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("上传资料")]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult UploadFiles(IFormFile file)
        {
            var res = new DResult();

            try
            {
                if (file == null)
                {
                    XTrace.WriteLine("获取到的文件为空");

                    res.msg = GetResource("资料上传有误");
                    return Json(res);
                }

                var OrignfileName = file.FileName;

                var filename = $"{Randoms.RandomStr(13).ToLower()}{Path.GetExtension(OrignfileName)}";
                var filepath = DHSetting.Current.UploadPath.CombinePath($"ProductData/{filename}");
                var saveFileName = filepath.GetFullPath();
                var f = saveFileName.AsFile();
                if (f.Exists)
                {
                    f.Delete();
                }
                saveFileName.EnsureDirectory();
                file.SaveAs(saveFileName);
                res.msg = GetResource("资料上传成功");
                res.success = true;
                res.data = new { OriginFileName = OrignfileName, FilePath = filepath.Replace("\\", "/") };
                return Json(res);
            }
            catch (Exception ex)
            {
                res.msg = GetResource("资料上传异常");
                XTrace.WriteLine($"资料上传异常：{ex.ToString()}");
                return Json(res);
            }
        }

        /// <summary>
        /// 添加产品资料
        /// </summary>
        /// <param name="ProductProjectId">产品项目id</param>
        /// <param name="FilePath">资料路径</param>
        /// <param name="ProductDataCategoryId">资料分类id</param>
        /// <param name="Remark">备注</param>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("添加")]
        [EntityAuthorize(PermissionFlags.Insert)]
        public IActionResult Add(Int32 ProductProjectId, String FilePath,Int32 ProductDataCategoryId,String Remark)
        {
            DResult res = new();
            if(ProductProjectId <= 0)
            {
                res.msg = GetResource("产品项目不能为空");
                return Json(res);
            }
            if (ProductDataCategoryId <= 0)
            {
                res.msg = GetResource("产品资料类别不能为空");
                return Json(res);
            }
            if (FilePath.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("产品资料文件不能为空");
                return Json(res);
            }
            var modelProductDataCategory = ProductDataCategory.FindById(ProductDataCategoryId);
            if (modelProductDataCategory == null)
            {
                res.msg = GetResource("产品资料类别信息不存在");
                return Json(res);
            }
            var modelProductProject = ProductProject.FindById(ProductProjectId);
            if (modelProductProject == null)
            {
                res.msg = GetResource("产品项目不存在");
                return Json(res);
            }
            var model = new ProductData()
            {
                FilePath = FilePath,
                ProductProjectId = modelProductProject.Id,
                ProductTypeId = modelProductProject.ProductTypeId,
                ProductDataCategoryId1 = modelProductDataCategory.ParentId,
                ProductDataCategoryId2 = modelProductDataCategory.Id,
                Remark = Remark,
            };
            model.Insert();
            res.success = true;
            res.msg = GetResource("添加成功");
            return Json(res);
        }

        /// <summary>
        /// 编辑产品资料
        /// </summary>
        /// <param name="Id">资料id</param>
        /// <returns></returns>
        [DisplayName("编辑")]
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult Update(Int32 Id)
        {
            var model = ProductData.FindById(Id);
            if (model == null)
            {
                return Content(GetResource("产品资料不存在"));
            }
            return View(model);
        }

        /// <summary>
        /// 编辑产品资料
        /// </summary>
        /// <param name="Id">资料id</param>
        /// <param name="ProductProjectId">产品项目id</param>
        /// <param name="FilePath">资料路径</param>
        /// <param name="ProductDataCategoryId">资料分类id</param>
        /// <param name="Remark">备注</param>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("编辑")]
        [EntityAuthorize(PermissionFlags.Update)]
        public IActionResult Update(Int32 Id, Int32 ProductProjectId, String FilePath, Int32 ProductDataCategoryId, String Remark)
        {
            DResult res = new DResult();
            if (ProductProjectId <= 0)
            {
                res.msg = GetResource("产品项目不能为空");
                return Json(res);
            }
            if (ProductDataCategoryId <= 0)
            {
                res.msg = GetResource("产品资料类别不能为空");
                return Json(res);
            }
            if (FilePath.IsNullOrWhiteSpace())
            {
                res.msg = GetResource("产品资料文件不能为空");
                return Json(res);
            }
            var modelProductDataCategory = ProductDataCategory.FindById(ProductDataCategoryId);
            if (modelProductDataCategory == null)
            {
                res.msg = GetResource("产品资料类别信息不存在");
                return Json(res);
            }
            var model = ProductData.FindById(Id);
            if(model == null)
            {
                res.msg = GetResource("产品资料信息不存在");
                return Json(res);
            }
            var modelProductProject = ProductProject.FindById(ProductProjectId);
            if (modelProductProject == null)
            {
                res.msg = GetResource("产品项目不存在");
                return Json(res);
            }
            model.ProductTypeId = modelProductProject.ProductTypeId;
            model.FilePath = FilePath;
            model.ProductProjectId = modelProductProject.Id;
            model.ProductDataCategoryId1 = modelProductDataCategory.ParentId;
            model.ProductDataCategoryId2 = modelProductDataCategory.Id;
            model.Remark = Remark;
            model.Update();
            res.success = true;
            res.msg = GetResource("编辑成功");
            return Json(res);
        }

        /// <summary>
        /// 删除产品资料
        /// </summary>
        /// <param name="Id">资料id</param>
        /// <returns></returns>
        [HttpPost]
        [DisplayName("删除")]
        [EntityAuthorize(PermissionFlags.Delete)]
        public IActionResult Delete(Int32 Id)
        {
            var res = new DResult();

            var model = ProductData.FindById(Id);
            if (model != null)
            {
                var file = model.FilePath!.GetFullPath().AsFile();
                if (file.Exists)
                {
                    file.Delete();
                }
                model.Delete();
            }

            res.success = true;
            res.msg = GetResource("删除成功");
            return Json(res);
        }
    }
}
